package com.fls.master.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 工作班组表基础信息
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@Schema(description = "工作班组表基础信息")
public class WorkTeamBaseInfo implements Serializable {

    private static final long serialVersionUID = 5770555232121254052L;

    @Schema(description = "主键")
    private String idWorkteam;

    @Schema(description = "班组编码")
    private String code;

    @Schema(description = "班组名称")
    private String name;

    @Schema(description = "所属组织")
    private String idOrg;

    @Schema(description = "组织名称")
    private String orgName;

    @Schema(description = "所属经营主体")
    private String idBizunit;

    @Schema(description = "经营主体名称")
    private String bizunitName;

    @Schema(description = "所属部门")
    private String idDepartment;

    @Schema(description = "部门名称")
    private String depName;

    @Schema(description = "班组对应仓库")
    private String idWarehouse;

    @Schema(description = "班组对应仓库pk")
    private String pkWarehouse;

    @Schema(description = "班组对应仓库编码")
    private String warehouseCode;

    @Schema(description = "班组对应仓库名称")
    private String warehouseName;

    @Schema(description = "班组对应货位")
    private String idWhpos;

    @Schema(description = "班组对应货位pk")
    private String pkWhpos;

    @Schema(description = "班组对应货位名称")
    private String whposName;

    @Schema(description = "班组对应货位编码")
    private String whposCode;

    @Schema(description = "班组默认归还仓库")
    private String idWhback;

    @Schema(description = "班组默认归还仓库名称")
    private String whbackName;

    @Schema(description = "组长")
    private String idHeadman;

    @Schema(description = "组长名称")
    private String headmanName;

    @Schema(description = "班组成员")
    private String members;

    @Schema(description = "租赁服务标识：0=否，1=是，默认0，参见yesorno")
    private String rentalServiceFlag;

    @Schema(description = "商业维修标识：0=否，1=是，默认0，参见yesorno")
    private String commercialMaintenanceFlag;

    @Schema(description = "是否外协：0=否，1=是，默认0，参见yesorno")
    private String ocunitFlag;

    @Schema(description = "外协单位")
    private String idOcunit;

    @Schema(description = "NC主键")
    private String ncPk;
}
