package com.fls.master.api;

import com.fls.master.api.model.WorkTeamBaseInfo;
import java.util.List;

/**
 * 工作班组服务
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface RemoteWorkTeamService {

    /**
     * 通过用户id获取班组基础信息
     *
     * @param userId 用户id
     * @return 班组基础信息
     */
    List<WorkTeamBaseInfo> getWorkTeamsByUserId(String userId);


    WorkTeamBaseInfo getWorkTeamById(String teamId);

    /**
     * 通用id列表查询班组信息列表
     *
     * @param idsWorkteam 班组id列表
     * @return 班组列表信息
     */
    List<WorkTeamBaseInfo> getWorkTeamsByIds(List<String> idsWorkteam);
}
