package com.fls.master.api;

import com.fls.master.api.model.ResourceInfo;

import java.util.List;

/**
 * 资源服务
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface RemoteResourceService {

    /**
     * 通过用户id获取班组基础信息
     *
     * @param resourceId 资源id
     * @return 资源信息
     */
    ResourceInfo getResourceById(String resourceId);

    /**
     * 通过资源编码生成单据号
     * @param resourceId 资源id
     * @return 单据号
     */
    String genBIllCode(String resourceId);

    /**
     * 通过资源id列表获取资源信息
     *
     * @param idsResource 资源id列表
     * @return 资源信息列表
     */
    List<ResourceInfo> getResourcesByIds(List<String> idsResource);
}
