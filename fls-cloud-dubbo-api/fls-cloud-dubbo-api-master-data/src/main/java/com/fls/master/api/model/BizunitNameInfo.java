package com.fls.master.api.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 经营主体信息
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BizunitNameInfo implements Serializable {

	private static final long serialVersionUID = 2259185651461532378L;

	@Schema(description = "主键")
	private String idBizunit;

	@Schema(description = "编码")
	private String code;

	@Schema(description = "名称")
	private String name;
}
