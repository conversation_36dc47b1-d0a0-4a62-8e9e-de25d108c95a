package com.fls.master.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 内部收货地址
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
public class InnerAddressVo {

    @Schema(description = "收货地址id")
    private String idAddress;

    @Schema(description = "内部收货地址id")
    private String idInnerAddress;

    @Schema(description = "收货地址")
    private String detail;

    @Schema(description = "收货单位")
    private String receivingUnit;

    @Schema(description = "收货单位组织id")
    private String idOrg;

    @Schema(description = "收货单位经营主体id")
    private String idBizunit;

    @Schema(description = "联系人id")
    private String idLinkman;

    @Schema(description = "联系人")
    private String linkmanName;

    @Schema(description = "联系人手机")
    private String tel;

    @Schema(description = "联系人手机")
    private String mobile;
}
