package com.fls.workorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.master.api.RemoteMasterDataService;
import com.fls.master.api.RemoteResourceService;
import com.fls.todo.api.RemoteTodoService;
import com.fls.todo.api.enums.TaskHandleStatusEnum;
import com.fls.todo.api.enums.TodoTaskStatusEnum;
import com.fls.todo.api.model.TaskTransferReq;
import com.fls.todo.api.model.TodoMessage;
import com.fls.todo.api.model.TodoTaskQuery;
import com.fls.todo.api.model.TodoTaskVo;
import com.fls.workflow.api.RemoteWorkflowService;
import com.fls.workflow.api.model.BackNodeInfo;
import com.fls.workflow.api.model.BackNodeReq;
import com.fls.workflow.api.model.TaskInfo;
import com.fls.workorder.constant.WorkorderConstant;
import com.fls.workorder.convert.OrderTaskRecordConvert;
import com.fls.workorder.convert.TodoMessageConvert;
import com.fls.workorder.entity.WorkorderRecordEntity;
import com.fls.workorder.entity.WorkorderTaskDefinitionEntity;
import com.fls.workorder.entity.WorkorderTaskRecordEntity;
import com.fls.workorder.enums.AssigneeTypeEnum;
import com.fls.workorder.enums.OperationTypeEnum;
import com.fls.workorder.enums.OrderStatusEnum;
import com.fls.workorder.enums.SubmitTypeEnum;
import com.fls.workorder.enums.TaskStatusEnum;
import com.fls.workorder.mapper.WorkorderTaskRecordMapper;
import com.fls.workorder.pojo.dto.OrderCreateTaskDTO;
import com.fls.workorder.pojo.dto.TaskAddDTO;
import com.fls.workorder.pojo.dto.TaskAssigneeUpdateDTO;
import com.fls.workorder.pojo.dto.TaskBackNodeOptDTO;
import com.fls.workorder.pojo.dto.TaskBackNodeQueryDTO;
import com.fls.workorder.pojo.dto.TaskCandidateUpdateDTO;
import com.fls.workorder.pojo.dto.TaskClaimDTO;
import com.fls.workorder.pojo.dto.TaskCoOperatorUpdateDTO;
import com.fls.workorder.pojo.dto.TaskCompleteDTO;
import com.fls.workorder.pojo.dto.TaskFollowDTO;
import com.fls.workorder.pojo.dto.TaskSuspendDTO;
import com.fls.workorder.pojo.dto.TaskTerminateDTO;
import com.fls.workorder.pojo.dto.TaskTransferDTO;
import com.fls.workorder.pojo.dto.UserNameDTO;
import com.fls.workorder.pojo.query.OrderTaskPageQuery;
import com.fls.workorder.pojo.query.TaskCenterQuery;
import com.fls.workorder.pojo.vo.TaskCenterDetailVo;
import com.fls.workorder.pojo.vo.TaskRecordDetailVo;
import com.fls.workorder.pojo.vo.TaskRecordVo;
import com.fls.workorder.pojo.vo.TaskSimpleVo;
import com.fls.workorder.service.IBaseAttrsService;
import com.fls.workorder.service.IOperationProduceService;
import com.fls.workorder.service.IWorkorderRecordService;
import com.fls.workorder.service.IWorkorderTaskDefinitionService;
import com.fls.workorder.service.IWorkorderTaskRecordService;
import com.fls.workorder.system.TaskThreadPoolExecutor;
import com.fls.workorder.utils.RabbitUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 工单任务信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkorderTaskRecordServiceImpl extends ServiceImpl<WorkorderTaskRecordMapper, WorkorderTaskRecordEntity>
    implements IWorkorderTaskRecordService {

    private final OrderTaskRecordConvert taskRecordConvert;

    @DubboReference
    private RemoteResourceService remoteResourceService;

    @DubboReference
    private RemoteMasterDataService remoteMasterDataService;

    @DubboReference
    private RemoteWorkflowService remoteWorkflowService;

    @DubboReference
    private RemoteTodoService remoteTodoService;

    private final TodoMessageConvert todoMessageConvert;

    private final RabbitTemplate rabbitTemplate;

    private final IWorkorderRecordService workorderRecordService;

    private final IWorkorderTaskDefinitionService workorderTaskDefinitionService;

    private final OrderTaskRecordConvert orderTaskRecordConvert;

    private final TaskThreadPoolExecutor taskThreadPoolExecutor;

    private final IOperationProduceService produceService;

    private final IBaseAttrsService baseAttrsService;

    @Override
    public List<WorkorderTaskRecordEntity> listByWorkorderId(String workorderId) {
        if (ObjectUtil.isEmpty(workorderId)) {
            throw new IllegalArgumentException("workorderId cannot be null or empty");
        }
        List<WorkorderTaskRecordEntity> result = lambdaQuery()
            .eq(WorkorderTaskRecordEntity::getIdWorkorderRecord, workorderId).list();
        log.info("Querying WorkorderTaskRecords from workorderId {}, result: {}", workorderId, result);
        return CollectionUtil.isEmpty(result) ? Collections.emptyList() : result;
    }

    @Override
    public List<TaskRecordDetailVo> voListByTaskId(String workorderId) {
        if (ObjectUtil.isEmpty(workorderId)) {
            throw new IllegalArgumentException("workorderId cannot be null or empty");
        }
        List<TaskRecordDetailVo> taskVoListByWorkorderId = baseMapper.getTaskVoListByWorkorderId(workorderId);
        return CollectionUtil.isEmpty(taskVoListByWorkorderId) ? Collections.emptyList() : taskVoListByWorkorderId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminateByWorkId(String workorderId, String operator) {
        List<WorkorderTaskRecordEntity> result = listByWorkorderId(workorderId);
        result.forEach(taskRecord -> {
            taskRecord.setTaskStatus(TaskStatusEnum.TERMINATE.getCode());
        });
        saveOrUpdateBatch(result);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTasksOperation(OperationTypeEnum.TASK_TERMINATE.getTitle(), result,
                WorkorderConstant.SYSTEM_OPERATOR);
        });
    }

    @Override
    public PageResult<TaskRecordVo> queryOrderTaskPage(OrderTaskPageQuery pageQuery) {
        Page<WorkorderTaskRecordEntity> page = new Page<>(pageQuery.getPageNo(), pageQuery.getPageSize());
        Page<WorkorderTaskRecordEntity> recordEntities = lambdaQuery()
            .eq(pageQuery.getStatus() != null, WorkorderTaskRecordEntity::getTaskStatus, pageQuery.getStatus())
            .eq(StrUtil.isNotEmpty(pageQuery.getTaskName()), WorkorderTaskRecordEntity::getTaskInstanceName,
                pageQuery.getTaskName())
            .eq(StrUtil.isNotEmpty(pageQuery.getTaskDefine()), WorkorderTaskRecordEntity::getIdTaskDefinition,
                pageQuery.getTaskDefine())
            .eq(StrUtil.isNotEmpty(pageQuery.getIdWorkorder()), WorkorderTaskRecordEntity::getIdWorkorderRecord,
                pageQuery.getIdWorkorder())
            .eq(StrUtil.isNotEmpty(pageQuery.getParticipator()), WorkorderTaskRecordEntity::getIdsAssignee,
                pageQuery.getParticipator())
            .between(WorkorderTaskRecordEntity::getCreateTime, pageQuery.getBegin() + " 00:00:00",
                pageQuery.getEnd() + " 23:59:59")
            .page(page);
        List<TaskRecordVo> taskRecordVos = taskRecordConvert.recordEntityToVoList(recordEntities.getRecords());
        return new PageResult<>(page, taskRecordVos);
    }

    @Override
    public String createOrderTask(TaskInfo taskInfo, String taskInstanceId) {
        // taskInfo转OrderCreateTaskDTO
        String taskDefinitionKey = taskInfo.getTaskDefinitionKey();
        if (StrUtil.isEmpty(taskDefinitionKey) || !taskDefinitionKey.startsWith("Resource_")) {
            throw new ServiceException("工单任务定义异常，请检查工单任务定义");
        }
        String taskDefId = StrUtil.removePrefix(taskDefinitionKey, "Resource_");
        taskInfo.setTaskDefId(taskDefId);
        OrderCreateTaskDTO orderCreateTaskDTO = getOrderCreateTaskDTO(taskInfo, taskInstanceId, taskDefId);
        return createOrderTask(orderCreateTaskDTO);
    }

    @NotNull
    private static OrderCreateTaskDTO getOrderCreateTaskDTO(TaskInfo taskInfo, String taskInstanceId, String taskDefId) {
        OrderCreateTaskDTO orderCreateTaskDTO = new OrderCreateTaskDTO();
        orderCreateTaskDTO.setAssignees(taskInfo.getAssignee());
        orderCreateTaskDTO.setCandidates(taskInfo.getCandidates());
        orderCreateTaskDTO.setIdProcInst(taskInfo.getProcessInstanceId());
        orderCreateTaskDTO.setIdProcTask(taskInfo.getId());
        orderCreateTaskDTO.setTaskName(taskInfo.getName());
        orderCreateTaskDTO.setIdTaskDefinition(taskDefId);
        orderCreateTaskDTO.setTaskInstanceId(taskInstanceId);
        return orderCreateTaskDTO;
    }

    @Override
    public String createOrderTask(OrderCreateTaskDTO createTaskDTO) {
        WorkorderTaskRecordEntity taskRecord = lambdaQuery()
            .eq(WorkorderTaskRecordEntity::getIdProctask, createTaskDTO.getIdProcTask()).one();
        if (ObjectUtil.isNotNull(taskRecord)) {
            log.info("task record already exist,task id:{},task procTaskId:{}", taskRecord.getIdWorkorderTaskRecord(),
                taskRecord.getIdProctask());
            return taskRecord.getIdWorkorderTaskRecord();
        }
        WorkorderRecordEntity workorderRecordEntity = validateAndGetWorkorder(createTaskDTO);
        WorkorderTaskDefinitionEntity taskDefinition = validateAndGetDefinition(createTaskDTO);
        createTaskDTO.setIdWorkorderRecord(workorderRecordEntity.getIdWorkorderRecord());
        WorkorderTaskRecordEntity workorderTaskRecordEntity = taskRecordConvert.taskDTOToEntity(createTaskDTO,
            taskDefinition, workorderRecordEntity);
        handleAutoAssign(workorderTaskRecordEntity, taskDefinition);

        //状态实例设置
        workorderTaskRecordEntity
            .setTaskStatus(StrUtil.isBlank(workorderTaskRecordEntity.getIdsCandidate()) ? TaskStatusEnum.INIT.getCode()
                : TaskStatusEnum.CLAIM.getCode());
        if (StrUtil.isNotEmpty(createTaskDTO.getTaskInstanceId())) {
            workorderTaskRecordEntity.setIdTaskInstance(createTaskDTO.getTaskInstanceId());
        }
        // 任务编号
        String taskCode = remoteResourceService.genBIllCode(workorderTaskRecordEntity.getIdTaskResource());
        workorderTaskRecordEntity.setTaskCode(taskCode);
        save(workorderTaskRecordEntity);
        // 更新工单状态为进行中，并发送待办消息
        workorderRecordEntity.setWorkorderStatus(OrderStatusEnum.PROGRESS.getCode());
        workorderRecordService.updateById(workorderRecordEntity);
        // 如果是指定用户的分配，则发送待办任务消息
        if (StrUtil.isNotEmpty(workorderTaskRecordEntity.getIdsAssignee())) {
            sendTodoMessage(workorderTaskRecordEntity, TodoTaskStatusEnum.PENDING_HANDLE);
        }
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_INIT.getTitle(), workorderTaskRecordEntity,
                WorkorderConstant.SYSTEM_OPERATOR);
            produceService.produceOrderOperation(OperationTypeEnum.PROGRESS.getTitle(), workorderRecordEntity,
                WorkorderConstant.SYSTEM_OPERATOR);
        });
        return workorderTaskRecordEntity.getIdWorkorderTaskRecord();
    }

    @NotNull
    private WorkorderTaskDefinitionEntity validateAndGetDefinition(OrderCreateTaskDTO createTaskDTO) {
        WorkorderTaskDefinitionEntity taskDefinition = workorderTaskDefinitionService
            .getById(createTaskDTO.getIdTaskDefinition());
        if (ObjectUtil.isNull(taskDefinition)) {
            log.error("system logic error, workorder task definition not found, definitionId:{}", taskDefinition);
            throw new ServiceException("工单任务定义不存在");
        }
        return taskDefinition;
    }

    @NotNull
    private WorkorderRecordEntity validateAndGetWorkorder(OrderCreateTaskDTO createTaskDTO) {
        WorkorderRecordEntity workorderRecordEntity = workorderRecordService
            .queryWorkOrderByProcIns(createTaskDTO.getIdProcInst());
        if (ObjectUtil.isNull(workorderRecordEntity)) {
            log.error("system logic error, workorder record not found, taskId:{}", createTaskDTO.getIdProcInst());
            throw new ServiceException("工单记录不存在");
        }
        return workorderRecordEntity;
    }

    private void handleAutoAssign(WorkorderTaskRecordEntity taskRecordEntity, WorkorderTaskDefinitionEntity taskDefinition) {
        if (taskDefinition.isAutoAssign() && taskRecordEntity.hasOnlyCandidate()) {
            String idProcTask = taskRecordEntity.getIdProctask();
            String[] idsCandidate = taskRecordEntity.getIdsCandidate().split(",");
            Arrays.sort(idsCandidate);
            //数组第一个为办理人，剩下的为候选人
            String idAssignee = idsCandidate[0];
            List<String> idsCoOperator = Arrays.asList(idsCandidate).subList(1, idsCandidate.length);
            remoteWorkflowService.updateAssignee(idProcTask, idAssignee, idsCoOperator);
            taskRecordEntity.setIdsCandidate(StrUtil.EMPTY);
            taskRecordEntity.setIdsAssignee(idAssignee);
            taskRecordEntity.setIdsCoOperator(StrUtil.join(",", idsCoOperator));
        }
    }

    @Override
    public String addOrderTask(TaskAddDTO taskAddDTO) {
        String idWorkorderRecord = taskAddDTO.getIdWorkorderRecord();
        String idTaskInstance = taskAddDTO.getIdTaskInstance();
        String idTaskDefinition = taskAddDTO.getIdTaskDefinition();
        WorkorderRecordEntity workorderRecordEntity = workorderRecordService.getById(idWorkorderRecord);
        if (ObjectUtil.isNull(workorderRecordEntity)) {
            throw new ServiceException("工单记录不存在");
        }
        // 添加了条件限制后，工单任务定义只能是同一个
        WorkorderTaskRecordEntity firstTaskRecord = lambdaQuery()
            .eq(WorkorderTaskRecordEntity::getIdWorkorderRecord, idWorkorderRecord)
            .in(WorkorderTaskRecordEntity::getTaskStatus, TaskStatusEnum.PROGRESS.getCode(),
                TaskStatusEnum.INIT.getCode())
            .orderByAsc(WorkorderTaskRecordEntity::getCreateTime).last("limit 1").one();
        if (ObjectUtil.isNull(firstTaskRecord)) {
            throw new ServiceException("工单记录未存在工单任务，新增工单任务失败");
        }
        if (!StrUtil.equalsIgnoreCase(idTaskDefinition, firstTaskRecord.getIdTaskDefinition())) {
            throw new ServiceException("工单任务定义不一致，新增工单任务失败");
        }
        TaskInfo taskInfo = remoteWorkflowService.addTask(workorderRecordEntity.getIdProcinst(), idTaskDefinition);
        if (ObjectUtil.isNotNull(taskInfo)) {
            return createOrderTask(taskInfo, idTaskInstance);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claimTask(TaskClaimDTO claimDTO) {
        // 校验工单任务记录
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(claimDTO.getIdTask());
        checkAssignee(taskRecord, claimDTO.getIdClaim());
        // 工作流执行认领操作
        remoteWorkflowService.claimTask(taskRecord.getIdProctask(), claimDTO.getIdClaim());
        // 操作成功后，执行认领人更新操作
        taskRecord.setIdsAssignee(claimDTO.getIdClaim());
        taskRecord.setTaskStatus(TaskStatusEnum.PROGRESS.getCode());
        saveOrUpdate(taskRecord);
        sendTodoMessage(taskRecord, TodoTaskStatusEnum.PENDING_HANDLE);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_CLAIM.getTitle(), taskRecord,
                claimDTO.getIdClaim());
        });
    }

    private WorkorderTaskRecordEntity checkTaskRecord(String taskId) {
        WorkorderTaskRecordEntity taskRecord = lambdaQuery()
            .eq(WorkorderTaskRecordEntity::getIdWorkorderTaskRecord, taskId).one();
        if (ObjectUtil.isNull(taskRecord)) {
            throw new ServiceException("工单任务记录不存在");
        }
        return taskRecord;
    }

    private void checkAssignee(WorkorderTaskRecordEntity taskRecord, String assignee) {
        // 判断办理人是否已存在
        if (StrUtil.isNotEmpty(taskRecord.getIdsAssignee())) {
            throw new ServiceException("工单任务办理人已存在");
        }
        // 候选人中包含认领人
        if (!taskRecord.getIdsCandidate().contains(assignee)) {
            throw new ServiceException("操作人无工单任务认领操作权限");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void followTask(TaskFollowDTO followDTO) {
        // 校验工单任务记录
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(followDTO.getIdTask());
        if (!StrUtil.equals(taskRecord.getIdsAssignee(), followDTO.getOperator())) {
            throw new ServiceException("操作人无工单任务操作权限");
        }
        // 工作流执行关注操作
        remoteWorkflowService.followUpTask(taskRecord.getIdProctask(), followDTO.getData());
        orderTaskRecordConvert.updateTaskRecordByFollowUp(followDTO, taskRecord);
        taskRecord.setTaskStatus(TaskStatusEnum.PROGRESS.getCode());
        taskRecord.setFormJson(JSONUtil.toJsonStr(followDTO.getData()));
        saveOrUpdate(taskRecord);
        sendTodoMessage(taskRecord, TodoTaskStatusEnum.PENDING_HANDLE);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_FOLLOW.getTitle(), taskRecord,
                followDTO.getOperator());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskSimpleVo transferTask(TaskTransferDTO transferDTO) {
        // 校验工单任务记录
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(transferDTO.getIdTask());
        if (!StrUtil.equals(taskRecord.getIdsAssignee(), transferDTO.getOperator()) && !transferDTO.getIsAdmin()) {
            throw new ServiceException("操作人无工单任务操作权限");
        }
        // 工作流执行关注操作
        remoteWorkflowService.transferTask(taskRecord.getIdProctask(), transferDTO.getTransUserId());
        taskRecord.setTaskStatus(TaskStatusEnum.PROGRESS.getCode());
        taskRecord.setIdsAssignee(transferDTO.getTransUserId());
        saveOrUpdate(taskRecord);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_TRANSFER.getTitle(), taskRecord,
                transferDTO.getOperator(), null, transferDTO.getRemark());
            try {
                TaskTransferReq transferReq = new TaskTransferReq(transferDTO.getIdTask(), transferDTO.getOperator(), transferDTO.getTransUserId());
                remoteTodoService.transfer(transferReq);
            } catch (Exception e) {
                log.error("handle todo transfer error, taskRecord: {}, e: {}", taskRecord, e.getMessage(), e);
            }
        });
        TaskSimpleVo taskSimpleVo = orderTaskRecordConvert.taskToSimpleVo(taskRecord);
        WorkorderRecordEntity workorderRecordEntity = workorderRecordService.getById(taskRecord.getIdWorkorderRecord());
        taskSimpleVo.setOrderStatus(workorderRecordEntity.getWorkorderStatus());
        return taskSimpleVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspendTask(TaskSuspendDTO suspendDTO) {
        String idTask = suspendDTO.getIdTask();
        // 校验工单任务记录
        WorkorderTaskRecordEntity taskRecord = lambdaQuery()
            .eq(WorkorderTaskRecordEntity::getIdWorkorderTaskRecord, idTask).one();
        if (ObjectUtil.isNull(taskRecord)) {
            throw new IllegalArgumentException("工单任务记录不存在");
        }
        String state = suspendDTO.getState();
        if (StrUtil.equals(state, "suspend") && TaskStatusEnum.isSuspend(taskRecord.getTaskStatus())) {
            throw new ServiceException("工单任务已挂起，请勿重复操作");
        }
        if (StrUtil.equals(state, "suspend") && TaskStatusEnum.PROGRESS.getCode() != taskRecord.getTaskStatus()) {
            throw new ServiceException("操作失败，非进行中任务不能执行工单任务挂起操作");
        }
        if (StrUtil.equals(state, "active") && !TaskStatusEnum.isSuspend(taskRecord.getTaskStatus())) {
            throw new ServiceException("工单任务已激活，请勿重复操作");
        }
        if (!StrUtil.equals(taskRecord.getIdsAssignee(), suspendDTO.getOperator())) {
            throw new ServiceException("操作人无工单任务操作权限");
        }
        // 移除工单挂起操作，此处是对流程定义进行挂起操作，会影响其他工单任务
        // remoteWorkflowService.updateState(state, taskRecord.getIdProctask());
        taskRecord.setTaskStatus(StrUtil.equals(state, "suspend") ? TaskStatusEnum.SUSPEND.getCode()
            : TaskStatusEnum.PROGRESS.getCode());
        saveOrUpdate(taskRecord);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(
                StrUtil.equals(state, "suspend") ? OperationTypeEnum.TASK_SUSPEND.getTitle()
                    : OperationTypeEnum.TASK_ACTIVE.getTitle(),
                taskRecord,
                suspendDTO.getOperator());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskRecordVo completeTask(TaskCompleteDTO completeDTO) {
        // 校验工单任务记录
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(completeDTO.getIdTask());
        String operator = completeDTO.getOperator();
        String submitType = SubmitTypeEnum.ASSIGNEE.getType();
        WorkorderTaskDefinitionEntity taskDefinition = workorderTaskDefinitionService.getById(taskRecord.getIdTaskDefinition());
        //协办人校验
        if (StrUtil.isNotBlank(taskRecord.getIdsCoOperator()) && taskRecord.getIdsCoOperator().contains(operator)) {
            if (ObjectUtil.isNotNull(taskDefinition) && StrUtil.equals(taskDefinition.getCoSubmitFlag(), WorkorderConstant.AUTO_COOPERATE_DISABLE)) {
                throw new ServiceException("协办人无工单任务操作权限");
            }
            operator = taskRecord.getIdsAssignee();
            submitType = SubmitTypeEnum.COOPERATE.getType();
        }
        if (!StrUtil.equals(taskRecord.getIdsAssignee(), operator)) {
            throw new ServiceException("操作人无工单任务操作权限");
        }
        TaskInfo taskInfo = remoteWorkflowService.completeTask(taskRecord.getIdProctask(), operator,
            completeDTO.getData());
        orderTaskRecordConvert.updateTaskRecordByComplete(completeDTO, taskRecord);
        taskRecord.setTaskStatus(TaskStatusEnum.END.getCode());
        taskRecord.setFormJson(JSONUtil.toJsonStr(completeDTO.getData()));
        taskRecord.setEndTime(LocalDateTime.now());
        taskRecord.setIdSubmitter(completeDTO.getOperator());
        taskRecord.setSubmitType(submitType);
        saveOrUpdate(taskRecord);
        TaskRecordVo nextTaskRecord = null;
        if (ObjectUtil.isNotNull(taskInfo)) {
            String nextTaskId = createOrderTask(taskInfo, completeDTO.getIdNextInstance());
            WorkorderTaskRecordEntity taskRecordEntity = getById(nextTaskId);
            nextTaskRecord = orderTaskRecordConvert.entityToVo(taskRecordEntity);
        } else {
            updateOrderStatus(taskRecord.getIdProcinst(), OrderStatusEnum.END, OperationTypeEnum.END);
        }
        sendTodoMessage(taskRecord, TodoTaskStatusEnum.COMPLETED);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_COMPLETE.getTitle(), taskRecord,
                completeDTO.getOperator(), null, completeDTO.getTaskRemarks());
        });
        return nextTaskRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskSimpleVo terminateTask(TaskTerminateDTO terminateDTO) {
        // 校验工单任务记录
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(terminateDTO.getIdTask());
        long terminateFlag = terminateDTO.getTerminate();
        if (terminateFlag == 0) {
            TaskCompleteDTO taskCompleteDTO = new TaskCompleteDTO();
            taskCompleteDTO.setIdTask(terminateDTO.getIdTask());
            taskCompleteDTO.setOperator(terminateDTO.getOperator());
            taskCompleteDTO.setData(terminateDTO.getData());
            completeTask(taskCompleteDTO);
        } else {
            remoteWorkflowService.stopProcess(taskRecord.getIdProcinst(), terminateDTO.getOperator());
            taskRecord.setTaskStatus(TaskStatusEnum.TERMINATE.getCode());
            taskRecord.setEndTime(LocalDateTime.now());
            saveOrUpdate(taskRecord);
            updateOrderStatus(taskRecord.getIdProcinst(), OrderStatusEnum.TERMINATE, OperationTypeEnum.TERMINATE);
            sendTodoMessage(taskRecord, TodoTaskStatusEnum.TERMINATED);
        }
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_TERMINATE.getTitle(), taskRecord,
                terminateDTO.getOperator());
        });
        TaskSimpleVo taskSimpleVo = orderTaskRecordConvert.taskToSimpleVo(taskRecord);
        WorkorderRecordEntity workorderRecordEntity = workorderRecordService.getById(taskRecord.getIdWorkorderRecord());
        taskSimpleVo.setOrderStatus(workorderRecordEntity.getWorkorderStatus());
        return taskSimpleVo;
    }

    public void updateOrderStatus(String procInsId, OrderStatusEnum orderStatus, OperationTypeEnum operation) {
        if (StrUtil.isEmpty(procInsId)) {
            throw new ServiceException("流程实例ID不能为空");
        }
        WorkorderRecordEntity workorderRecord = workorderRecordService.queryWorkOrderByProcIns(procInsId);
        if (ObjectUtil.isNull(workorderRecord)) {
            throw new ServiceException("工单记录不存在");
        }
        workorderRecord.setWorkorderStatus(orderStatus.getCode());
        if (orderStatus == OrderStatusEnum.END || orderStatus == OrderStatusEnum.TERMINATE) {
            workorderRecord.setEndTime(LocalDateTime.now());
        }
        workorderRecordService.updateById(workorderRecord);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceOrderOperation(operation.getTitle(), workorderRecord, workorderRecord.getCreateBy());
        });
    }

    @Override
    public TaskSimpleVo updateAssignee(TaskAssigneeUpdateDTO assigneeUpdateDTO) {
        String idTask = assigneeUpdateDTO.getIdTask();
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(idTask);
        // 如果是认领走前置校验
        Integer operateType = assigneeUpdateDTO.getOperateType();
        String idsAssignee = taskRecord.getIdsAssignee();
        String idsCandidate = taskRecord.getIdsCandidate();
        // 判断办理人是否为空
        if (StrUtil.isNotEmpty(idsAssignee)) {
            throw new ServiceException("工单任务办理人已存在");
        }
        if (operateType == AssigneeTypeEnum.CLAIM.getTypeInt()) {
            if (!StrUtil.contains(idsCandidate, assigneeUpdateDTO.getAssignee())) {
                throw new ServiceException("操作人无工单任务操作权限");
            }
        }
        List<String> coOperator = assigneeUpdateDTO.getIdsCoOperator();
        if (coOperator != null && idsAssignee != null) {
            coOperator.remove(idsAssignee);
        }
        // 调用远程服务更新办理人和候选人
        String idsCoOperators = StrUtil.join(",", coOperator);
        remoteWorkflowService.updateAssignee(taskRecord.getIdProctask(), assigneeUpdateDTO.getAssignee(),
            coOperator);
        taskRecord.setIdsAssignee(assigneeUpdateDTO.getAssignee());
        taskRecord.setIdsCoOperator(idsCoOperators);
        taskRecord.setUpdateTime(LocalDateTime.now());
        taskRecord.setUpdateBy(assigneeUpdateDTO.getOperator());
        taskRecord.setAssigneeType(AssigneeTypeEnum.matchType(operateType));
        taskRecord.setDeadline(assigneeUpdateDTO.getDeadline());
        if (StrUtil.isNotBlank(taskRecord.getIdsAssignee())) {
            taskRecord.setTaskStatus(TaskStatusEnum.PROGRESS.getCode());
        }
        updateById(taskRecord);
        if (StrUtil.isNotEmpty(assigneeUpdateDTO.getAssignee())) {
            sendTodoMessage(taskRecord, TodoTaskStatusEnum.PENDING_HANDLE);
        }
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(
                operateType == AssigneeTypeEnum.ASSIGN.getTypeInt() ? OperationTypeEnum.TASK_ASSIGN.getTitle()
                    : OperationTypeEnum.TASK_CLAIM.getTitle(),
                taskRecord, assigneeUpdateDTO.getAssignee());
        });
        TaskSimpleVo taskSimpleVo = orderTaskRecordConvert.taskToSimpleVo(taskRecord);
        WorkorderRecordEntity workorderRecordEntity = workorderRecordService.getById(taskRecord.getIdWorkorderRecord());
        taskSimpleVo.setOrderStatus(workorderRecordEntity.getWorkorderStatus());
        return taskSimpleVo;
    }

    @Override
    public void updateCoOperator(TaskCoOperatorUpdateDTO coOperatorUpdateDTO) {
        // 前置校验
        String idTask = coOperatorUpdateDTO.getIdTask();
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(idTask);
        String idsAssignee = taskRecord.getIdsAssignee();
        if (StrUtil.isEmpty(idsAssignee)) {
            throw new ServiceException("工单任务办理人不存在，无法更新协办人");
        }
        List<String> coOperator = coOperatorUpdateDTO.getIdsCoOperator();
        if (coOperator != null) {
            coOperator.remove(idsAssignee);
        }
        // 更新协办人
        String idsCoOperator = StrUtil.join(",", coOperator);
        remoteWorkflowService.updateCoOperator(taskRecord.getIdProctask(), idsCoOperator);
        taskRecord.setIdsCoOperator(idsCoOperator);
        taskRecord.setUpdateTime(LocalDateTime.now());
        taskRecord.setUpdateBy(coOperatorUpdateDTO.getOperator());
        updateById(taskRecord);
        sendTodoMessage(taskRecord, TodoTaskStatusEnum.PENDING_HANDLE);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_COOPERATE.getTitle(), taskRecord,
                coOperatorUpdateDTO.getOperator());
        });
    }

    @Override
    public void updateCandidate(TaskCandidateUpdateDTO candidateUpdateDTO) {
        // 前置校验
        String idTask = candidateUpdateDTO.getIdTask();
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(idTask);
        if (StrUtil.isNotEmpty(taskRecord.getIdsAssignee())) {
            throw new ServiceException("工单任务办理人已存在");
        }
        // 更新候选人
        String idsCandidate = StrUtil.join(",", candidateUpdateDTO.getIdsCandidate());
        remoteWorkflowService.updateCandidates(taskRecord.getIdProctask(), idsCandidate);
        taskRecord.setIdsCandidate(idsCandidate);
        taskRecord.setUpdateTime(LocalDateTime.now());
        taskRecord.setUpdateBy(candidateUpdateDTO.getOperator());
        taskRecord.setTaskStatus(TaskStatusEnum.CLAIM.getCode());
        updateById(taskRecord);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_CANDIDATE.getTitle(), taskRecord,
                candidateUpdateDTO.getOperator());
        });
    }

    private void sendTodoMessage(WorkorderTaskRecordEntity taskRecord, TodoTaskStatusEnum todoTaskStatusEnum) {
        WorkorderTaskDefinitionEntity taskDefinition = workorderTaskDefinitionService
            .getById(taskRecord.getIdTaskDefinition());
        TodoMessage todoMessage = todoMessageConvert.orderTaskToMessage(taskRecord, taskDefinition);
        WorkorderRecordEntity workorderRecord = workorderRecordService.getById(taskRecord.getIdWorkorderRecord());
        todoMessage.setSourceRecordCode(workorderRecord.getOrderCode());
        todoMessage.setTaskStatus(todoTaskStatusEnum.getCode());
        String handleStatus = TodoTaskStatusEnum.isComplete(todoTaskStatusEnum.getCode()) ? TaskHandleStatusEnum.COMPLETED.getCode() : TaskHandleStatusEnum.WAIT_HANDLE.getCode();
        todoMessage.setHandleStatus(handleStatus);
        RabbitUtil.sendTodoMessage(todoMessage);
    }

    @Override
    public List<BackNodeInfo> getBackNodes(TaskBackNodeQueryDTO backNodeQueryDTO) {
        WorkorderTaskRecordEntity taskRecord = checkTaskInProgress(backNodeQueryDTO.getIdTask());
        return remoteWorkflowService.getBackNodes(taskRecord.getIdProctask());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskRecordVo backNode(TaskBackNodeOptDTO backNodeOptDTO) {
        String taskId = backNodeOptDTO.getIdTask();
        String operator = backNodeOptDTO.getOperator();
        WorkorderTaskRecordEntity taskRecord = checkTaskInProgress(taskId);
        if (!StrUtil.equals(taskRecord.getIdsAssignee(), operator)) {
            throw new ServiceException("操作人无工单任务操作权限");
        }
        BackNodeReq backNodeReq = new BackNodeReq();
        backNodeReq.setTaskId(taskRecord.getIdProctask());
        BeanUtil.copyProperties(backNodeOptDTO, backNodeReq);
        String backIdTask = remoteWorkflowService.backNode(backNodeReq);
        taskRecord.setTaskStatus(TaskStatusEnum.END.getCode());
        taskRecord.setUpdateTime(LocalDateTime.now());
        taskRecord.setUpdateBy(operator);
        updateById(taskRecord);
        sendTodoMessage(taskRecord, TodoTaskStatusEnum.COMPLETED);
        WorkorderTaskRecordEntity backTask = getAndUpdateBackNodeTask(taskRecord.getIdProcinst(),
            backNodeOptDTO.getBackTaskDefKey(), backIdTask);
        taskThreadPoolExecutor.putTask(() -> {
            produceService.produceTaskOperation(OperationTypeEnum.TASK_BACK_NODE.getTitle(), taskRecord,
                backNodeOptDTO.getOperator(), null, backNodeOptDTO.getMessage());
        });
        return orderTaskRecordConvert.entityToVo(backTask);
    }

    @NotNull
    private WorkorderTaskRecordEntity checkTaskInProgress(String taskId) {
        WorkorderTaskRecordEntity taskRecord = checkTaskRecord(taskId);
        if (TaskStatusEnum.PROGRESS.getCode() != taskRecord.getTaskStatus()
            && TaskStatusEnum.INIT.getCode() != taskRecord.getTaskStatus()) {
            throw new ServiceException("非进行中状态工单任务");
        }
        return taskRecord;
    }

    @NotNull
    private WorkorderTaskRecordEntity getAndUpdateBackNodeTask(String idProcInst, String taskDefKey,
                                                               String backIdTask) {
        String idFinishedTaskByTaskDefKey = remoteWorkflowService.getIdFinishedTaskByTaskDefKey(idProcInst, taskDefKey);
        WorkorderTaskRecordEntity backTask = lambdaQuery()
            .eq(WorkorderTaskRecordEntity::getIdProctask, idFinishedTaskByTaskDefKey).one();
        backTask.setIdProctask(backIdTask);
        backTask.setTaskStatus(TaskStatusEnum.PROGRESS.getCode());
        backTask.setUpdateTime(LocalDateTime.now());
        backTask.setUpdateBy(WorkorderConstant.SYSTEM_OPERATOR);
        backTask.setEndTime(null);
        updateById(backTask);
        sendTodoMessage(backTask, TodoTaskStatusEnum.PENDING_HANDLE);
        return backTask;
    }

    @Override
    public Page<TodoTaskVo> queryUnclaimedTasks(TodoTaskQuery query) {
        Page<TodoTaskVo> page = new Page<>(query.getPageNo(), query.getPageSize());
        Page<TodoTaskVo> taskPage = baseMapper.getTaskDetailByParam(page, query);
        // 返回结果
        return taskPage;
    }

    @Override
    public PageResult<TaskCenterDetailVo> queryTaskCenterPage(TaskCenterQuery query) {
        Page<TaskCenterDetailVo> page = new Page<>(query.getPageNo(), query.getPageSize());
        String userId = query.getUserId();
        String href = query.getHref();
        if (StrUtil.isNotBlank(userId) && StrUtil.isNotBlank(href)) {
            List<String> authBizunitIds = remoteMasterDataService.getAuthBizunitIds(userId, href);
            if (CollUtil.isEmpty(authBizunitIds)) {
                return new PageResult<>(page, Collections.emptyList());
            }
            query.setAuthBizunitIds(authBizunitIds);
        }
        // 分页查询
        List<TaskCenterDetailVo> records = baseMapper.selectTaskCenterPage(page, query);
        addTransFields(records);
        return new PageResult<>(page, records);
    }

    /**
     * 补充任务记录中的用户名称（主办人、候选人、协办人）
     *
     * @param taskVos 任务记录列表
     */
    public void addTransFields(List<TaskCenterDetailVo> taskVos) {
        // 收集所有用户 ID
        Set<String> idsUser = taskVos.stream().flatMap(taskVo -> {
            Set<String> ids = new HashSet<>();
            if (StrUtil.isNotBlank(taskVo.getIdsAssignee())) {
                ids.add(taskVo.getIdsAssignee());
            }
            ids.add(taskVo.getInitiator());
            ids.addAll(StrUtil.split(taskVo.getIdsCandidate(), ","));
            ids.addAll(StrUtil.split(taskVo.getIdsCoOperator(), ","));
            return ids.stream();
        }).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(idsUser)) {
            // 查询用户 ID → 姓名映射
            List<UserNameDTO> userIdNames = baseAttrsService.queryNamesByIds(idsUser);
            if (ObjectUtil.isNotEmpty(userIdNames)) {
                Map<String, String> nameMaps = userIdNames.stream().collect(Collectors.toMap(UserNameDTO::getIdUser, UserNameDTO::getName));
                // 设置任务人员名称
                for (TaskCenterDetailVo taskVo : taskVos) {
                    taskVo.setAssigneeName(nameMaps.get(taskVo.getIdsAssignee()));
                    taskVo.setInitiatorName(nameMaps.get(taskVo.getInitiator()));
                    taskVo.setCandidateNames(joinUserNames(taskVo.getIdsCandidate(), nameMaps));
                    taskVo.setCoOperatorNames(joinUserNames(taskVo.getIdsCoOperator(), nameMaps));
                }
            }
        }
        // 状态翻译
        taskVos.forEach(TaskCenterDetailVo::fillTransField);
    }

    private String joinUserNames(String idStr, Map<String, String> userIdNames) {
        return StrUtil.split(idStr, ",").stream().map(userIdNames::get).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
    }

    @Override
    public TaskRecordDetailVo votByTaskId(String idTask) {
        TaskRecordDetailVo taskDetailVo = baseMapper.getTaskVoByIdTask(idTask);
        if (ObjectUtil.isNull(taskDetailVo)) {
            throw new ServiceException("工单任务信息不存在");
        }
        enrichTaskRecordUserNames(taskDetailVo);
        return taskDetailVo;
    }

    /**
     * 补充任务记录中的用户名称（主办人、候选人、协办人）
     *
     * @param taskVo 任务记录列表
     */
    public void enrichTaskRecordUserNames(TaskRecordDetailVo taskVo) {
        // 收集所有用户 ID
        Set<String> ids = new HashSet<>();
        if (StrUtil.isNotBlank(taskVo.getIdsAssignee())) {
            ids.add(taskVo.getIdsAssignee());
        }
        ids.addAll(StrUtil.split(taskVo.getIdsCoOperator(), ","));
        ids.addAll(StrUtil.split(taskVo.getIdsCandidate(), ","));
        // 查询用户 ID → 姓名映射
        List<UserNameDTO> userIdNames = baseAttrsService.queryNamesByIds(ids);
        if (ObjectUtil.isNotEmpty(userIdNames)) {
            Map<String, String> nameMaps = userIdNames.stream().collect(Collectors.toMap(UserNameDTO::getIdUser, UserNameDTO::getName));
            taskVo.setAssigneeName(nameMaps.get(taskVo.getIdsAssignee()));
            taskVo.setCandidateNames(joinUserNames(taskVo.getIdsCandidate(), nameMaps));
            taskVo.setCoOperatorNames(joinUserNames(taskVo.getIdsCoOperator(), nameMaps));
        }
    }
}
