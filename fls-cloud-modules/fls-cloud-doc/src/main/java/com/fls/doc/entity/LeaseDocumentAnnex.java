package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料附加参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_lease_document_annex")
public class LeaseDocumentAnnex implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 附加参数表id
     */
    @TableId("id_annex")
    private String idAnnex;

    /**
     * 资料id
     */
    @TableField("id_doc")
    private String idDoc;

    /**
     * 资料分类id
     */
    @TableField("id_doc_cat")
    private String idDocCat;

    /**
     * 自定义值1
     */
    @TableField("val1")
    private String val1;

    /**
     * 自定义项2
     */
    @TableField("val2")
    private String val2;

    /**
     * 自定义项
     */
    @TableField("val3")
    private String val3;

    /**
     * 自定义项4
     */
    @TableField("val4")
    private String val4;

    /**
     * 自定义项5
     */
    @TableField("val5")
    private String val5;

    /**
     * 自定义项6
     */
    @TableField("val6")
    private String val6;

    /**
     * 自定义项7
     */
    @TableField("val7")
    private String val7;

    /**
     * 自定义项8
     */
    @TableField("val8")
    private String val8;

    /**
     * 自定义项9
     */
    @TableField("val9")
    private String val9;

    /**
     * 自定义项10
     */
    @TableField("val10")
    private String val10;

    /**
     * 自定义项11
     */
    @TableField("val11")
    private String val11;

    /**
     * 自定义项12
     */
    @TableField("val12")
    private String val12;

    /**
     * 自定义项13
     */
    @TableField("val13")
    private String val13;

    /**
     * 自定义项14
     */
    @TableField("val14")
    private String val14;

    /**
     * 自定义项15
     */
    @TableField("val15")
    private String val15;

    /**
     * 自定义项16
     */
    @TableField("val16")
    private String val16;

    /**
     * 自定义项17
     */
    @TableField("val17")
    private String val17;

    /**
     * 自定义项18
     */
    @TableField("val18")
    private String val18;

    /**
     * 自定义项19
     */
    @TableField("val19")
    private String val19;

    /**
     * 自定义项20
     */
    @TableField("val20")
    private String val20;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 1:删除
     */
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;
}
