package com.fls.doc.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fls.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 审批流工具类
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApprovalUtils {
    private final ApprovalConfing approvalConfing;
    private final RestTemplate restTemplate;

    public JSONObject submitAudit(String submitMethod, String resourceCode, String idResobj){
        return submitAudit(submitMethod,resourceCode,idResobj,null);
    }

    public JSONObject submitAudit(String submitMethod,String resourceCode,String idResobj,String idUser){
        return submitAudit(submitMethod,resourceCode,idResobj,idUser,null,null,null,null,null,null);
    }

    public JSONObject submitAudit(String submitMethod, String idUser, Integer pageNo, Integer pageSize,String projectName) {
        return submitAudit(submitMethod,null,null,idUser,null,null,null,pageNo,pageSize,projectName);
    }

    public JSONObject submitAudit(String submitMethod, String resourceCode, String idResobj, String idUser, String idProcaudittask, String auditInfo, String auditType) {
        return submitAudit(submitMethod,resourceCode,idResobj,idUser,idProcaudittask,auditInfo,auditType,null,null,null);
    }

    public JSONObject submitAudit(String submitMethod,String resourceCode,String idResobj,String idUser,String idProcaudittask,String auditInfo,String auditType,Integer pageNo, Integer pageSize,String projectName){
        HashMap<String, Object> map = new HashMap<>();
        map.put("c","fls");
        map.put("s","workflow");
        map.put("v","1");
        map.put("t",new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));
        map.put("m",submitMethod);
        map.put("resource_code",resourceCode);
        map.put("id_resobj",idResobj);
        map.put("id_user", idUser);
        map.put("id_procaudittask", idProcaudittask);
        map.put("audit_info", auditInfo);
        map.put("audit_type", auditType);
        map.put("page_no", pageNo);
        map.put("page_size", pageSize);
        map.put("project_name", projectName);
        List<String> p = new ArrayList<>();
        map.forEach((k, v) -> {
            if(null!=v){
                p.add(k + "=" + v);
            }
        });
        String url = approvalConfing.getUrl()+"?"+ StringUtils.join(p,"&");
        log.info("审批请求信息：url={}",url);
        String response = restTemplate.getForObject(url,String.class);
        log.info("审批返回结果：response={}",response);
        JSONObject jsonObject = JSONUtil.parseObj(response);
        if(!"00".equals(jsonObject.getStr("c"))) {
            throw new ServiceException(jsonObject.getStr("m"));
        }
        return jsonObject;
    }
}
