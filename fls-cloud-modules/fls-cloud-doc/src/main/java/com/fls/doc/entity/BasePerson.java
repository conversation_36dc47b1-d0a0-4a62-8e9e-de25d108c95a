package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 员工表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_person")
public class BasePerson implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id_person")
    private String idPerson;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 曾用名
     */
    @TableField("usedname")
    private String usedname;

    /**
     * 性别 1=男，2=女，3=不详 参见sex
     */
    @TableField("sex")
    private String sex;

    /**
     * 民族
     */
    @TableField("nation")
    private String nation;

    /**
     * 办公电话
     */
    @TableField("officephone")
    private String officephone;

    /**
     * 手机
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 所属组织
     */
    @TableField("id_org")
    private String idOrg;

    /**
     * 所属部门
     */
    @TableField("id_department")
    private String idDepartment;

    /**
     * 所属岗位
     */
    @TableField("id_post")
    private String idPost;

    /**
     * NC人员基本信息pk值
     */
    @TableField("pk_psndoc")
    private String pkPsndoc;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    private String deleteFlag;

    /**
     * 是否在岗，参见yesorno
     */
    @TableField("post_flag")
    private String postFlag;

    /**
     * 所属职务
     */
    @TableField("id_job")
    private String idJob;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 钉钉识别码id
     */
    @TableField("dingdid")
    private String dingdid;
}
