package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料维护申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_lease_document_datamaintainance")
public class LeaseDocumentDatamaintainance implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId("id_bill_doc")
    private String idBillDoc;

    /**
     * 单据号
     */
    @TableField("bill_code")
    private String billCode;

    /**
     * 单据日期
     */
    @TableField("bill_date")
    private Date billDate;

    /**
     * 单据备注信息
     */
    @TableField("memo")
    private String memo;

    /**
     * 集团
     */
    @TableField("id_group")
    private String idGroup;

    /**
     * 组织
     */
    @TableField("id_org")
    private String idOrg;

    /**
     * 经营主体
     */
    @TableField("id_bizunit")
    private String idBizunit;

    /**
     * 存储资源id
     */
    @TableField("id_resource")
    private String idResource;

    /**
     * 交易类型
     */
    @TableField("id_res_trantype")
    private String idResTrantype;

    /**
     * 是否启用审批流  参见字典枚举项“yesorno”
     */
    @TableField("needproc_flag")
    private String needprocFlag;

    /**
     * 审批后业务是否执行
     */
    @TableField("proc_do_flag")
    private String procDoFlag;

    /**
     * 单据状态 参见字典枚举项“sheet_status” 1:新增，2：变更、3：停用
     */
    @TableField("status")
    private String status;

    /**
     * 作废标识 默认值：'0'，参见“yesorno”
     */
    @TableField("invalid_flag")
    private String invalidFlag;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 审批人
     */
    @TableField("auditor")
    private String auditor;

    /**
     * 审批时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 作废人
     */
    @TableField("invalider")
    private String invalider;

    /**
     * 作废时间
     */
    @TableField("invalid_time")
    private Date invalidTime;

    /**
     * 删除标识 参见字典枚举项“yesorno”
     */
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 来源哪个表
     */
    @TableField("id_source_type")
    private String idSourceType;

    /**
     * 来源单据id
     */
    @TableField("id_source_h")
    private String idSourceH;

    /**
     * 来源单据子项id
     */
    @TableField("id_source_b")
    private String idSourceB;

    /**
     * 租赁资产id
     */
    @TableField("id_leaseassets")
    private String idLeaseassets;

    /**
     * 资料id
     */
    @TableField("id_doc")
    private String idDoc;

    /**
     * 负责人
     */
    @TableField("id_person")
    private String idPerson;

    /**
     * 实体ID
     */
    @TableField("entity_id")
    private String entityId;

    /**
     * 实体明细ID
     */
    @TableField("entity_detail_id")
    private String entityDetailId;

    /**
     * 资源实体对象名称， 一般为单号
     */
    @TableField("entity_name")
    private String entityName;

    /**
     * 有效时间，开始时间
     */
    @TableField("begin_time")
    private String beginTime;

    /**
     * 有效时间，结束时间
     */
    @TableField("end_time")
    private String endTime;
}
