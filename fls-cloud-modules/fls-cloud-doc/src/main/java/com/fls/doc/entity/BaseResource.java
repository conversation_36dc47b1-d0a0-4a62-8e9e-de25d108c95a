package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_resource")
public class BaseResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id_resource")
    private String idResource;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属分类
     */
    @TableField("id_resourceclass")
    private String idResourceclass;

    /**
     * 业务模块代码
     */
    @TableField("module_code")
    private String moduleCode;

    /**
     * 是否启用审批流：0=否，1=是，默认1，参见yesorno
     */
    @TableField("needproc_flag")
    private String needprocFlag;

    /**
     * 是否进入综合待审列表：0=否，1=是，默认1，参见yesorno
     */
    @TableField("incal_flag")
    private String incalFlag;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    private String deleteFlag;

    /**
     * 是否启用数据权限控制：0=否，1=是，默认1，参见yesorno
     */
    @TableField("needauth_flag")
    private String needauthFlag;

    /**
     * 资源审核页url地址
     */
    @TableField("incal_link")
    private String incalLink;

    /**
     * 类型，1=档案类，2=单据类
     */
    @TableField("resource_type")
    private String resourceType;

    /**
     * 是否启用资料管理，0=否，1=是，参见yesorno
     */
    @TableField("needdoc_flag")
    private String needdocFlag;

    /**
     * 是否启用申请，0=否，1=是，参见yesorno
     */
    @TableField("needapply_flag")
    private String needapplyFlag;

    /**
     * 是否启用档案数据管理，0=否，1=是，参见yesorno
     */
    @TableField("archmana_flag")
    private String archmanaFlag;

    /**
     * 是否启用历史记录，0=否，1=是，参见yesorno
     */
    @TableField("history_flag")
    private String historyFlag;

    /**
     * 是否进入移动APP：0=否，1=是，默认0，参见yesorno
     */
    @TableField("app_flag")
    private String appFlag;

    /**
     * 移动端动态路由url
     */
    @TableField("app_link")
    private String appLink;

    /**
     * 域名判断标识
     */
    @TableField("domain_flag")
    private String domainFlag;

    /**
     * 资源资料是否开启引用
     */
    @TableField("quote_flag")
    private String quoteFlag;
}
