package com.fls.doc.utils;


import cn.hutool.core.util.ObjectUtil;

/**
 * <AUTHOR>
 */
public class GetAutoCodeUtil {
    public static String getFlowNumber(Integer nowNumbers, Short len){
        return getFlowNumber(null, nowNumbers, len);
    }
    public static String getFlowNumber(String prefix, Integer nowNumbers, Short len){
        StringBuilder numbers = new StringBuilder();
        if (ObjectUtil.isNotEmpty(prefix)) {
            numbers.append(prefix);
        }
        int length = ObjectUtil.isEmpty(prefix)?len:len-prefix.length();
        String str = String.format(("%0" + length + "d"), nowNumbers+1);
        numbers.append(str);
        return String.valueOf(numbers);
    }

    public static void main(String[] args) {
        String aaaa = getFlowNumber(3245, (short) 4);
        System.out.println("args = " + aaaa);
    }
}
