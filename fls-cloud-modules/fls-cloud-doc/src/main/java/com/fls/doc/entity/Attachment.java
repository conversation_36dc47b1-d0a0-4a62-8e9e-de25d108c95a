package com.fls.doc.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fls.common.core.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Data
@Builder
@Accessors(chain = true)
@TableName("t_attachment")
@AllArgsConstructor
@NoArgsConstructor
public class Attachment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id_attachment")
    private String idAttachment;

    /**
     * 附件链接id
     */
    @TableField("id_link")
    private String idLink;

    /**
     * 路径
     */
    @TableField("path")
    private String path;

    /**
     * 文件类型
     */
    @TableField("type")
    private String type;

    /**
     * 文件名
     */
    @TableField("name")
    private String name;

    /**
     * 后缀名
     */
    @TableField("suffix")
    private String suffix;

    /**
     * 文件大小，字节数
     */
    @TableField("size")
    private Integer size;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JSONField(serialize = true, format = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    @JSONField(serialize = false)
    private String creator;

    /**
     * 作废时间
     */
    @TableField("invalid_time")
    @JSONField(serialize = false)
    private Date invalidTime;

    /**
     * 作废人
     */
    @TableField("invalider")
    @JSONField(serialize = false)
    private String invalider;

    /**
     * 时间戳
     */
    @TableField("ts")
    @JSONField(serialize = false)
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    @TableLogic
    @JSONField(serialize = false)
    private String deleteFlag;
}
