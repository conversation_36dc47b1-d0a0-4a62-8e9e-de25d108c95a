package com.fls.doc.mapper;

import com.fls.doc.entity.LeaseDocumentAnnex;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fls.doc.param.DocumentAnnexParam;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * LA资料附加参数表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
public interface LeaseDocumentAnnexMapper extends BaseMapper<LeaseDocumentAnnex> {

    Boolean saveAnnex(DocumentAnnexParam param);

    DocumentAnnexParam selectAnnex(@Param("idDoc") String idDoc,@Param("idDocCat") String idDocCat, @Param("valTable") String valTable);
}
