package com.fls.doc.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ZipUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.Attachment;
import com.fls.doc.entity.BaseResource;
import com.fls.doc.entity.BaseResourceclass;
import com.fls.doc.mapper.AttachmentMapper;
import com.fls.doc.service.AttachmentService;
import com.fls.doc.service.BaseResourceService;
import com.fls.doc.service.BaseResourceclassService;
import com.fls.doc.utils.FtpUtil;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 附件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AttachmentServiceImpl extends MPJBaseServiceImpl<AttachmentMapper, Attachment> implements AttachmentService {
    private final FtpUtil ftpUtil;
    private final BaseResourceService resourceService;
    private final BaseResourceclassService resourceclassService;

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<Attachment> uploads(MultipartFile[] files, String identity, String idResource, String idLink, String idUser) {
        if (ObjectUtil.isEmpty(idLink)) {
            idLink = IdUtil.randomUUID();
        }
        Date now = new Date();
        List<Attachment> fileEntities = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                String idAttachment = IdUtil.randomUUID();
                InputStream inputStream = file.getInputStream();
                String fileName = file.getOriginalFilename();
                assert fileName != null;
                String suffix = fileName.substring(fileName.lastIndexOf("."));
                StringBuilder uploadDir = new StringBuilder().append(ftpUtil.getPath(identity)).append("/");
                if (ObjectUtil.isNotEmpty(idResource)) {
                    BaseResource resource = resourceService.getById(idResource);
                    if (ObjectUtil.isNotEmpty(resource)) {
                        BaseResourceclass resourceclass = resourceclassService.getById(resource.getIdResourceclass());
                        if (ObjectUtil.isNotEmpty(resourceclass)) {
                            String resourceType = getResourceType(resourceclass.getCode());
                            uploadDir.append(resourceType).append("/");
                        }
                        uploadDir.append(resource.getCode()).append("/");
                    }
                }
                //年月目录结构
                String dateDir = DateUtil.format(now, DatePattern.SIMPLE_MONTH_PATTERN);
                //上传基础目录+年月目录结构
                uploadDir.append(dateDir);
                ftpUtil.upload(uploadDir.toString(), identity, idAttachment, inputStream);
                Attachment build = Attachment.builder()
                    .idAttachment(idAttachment)
                    .name(fileName)
                    .idLink(idLink)
                    .path(uploadDir.toString())
                    .size((int) file.getSize())
                    .suffix(suffix)
                    .type(file.getContentType())
                    .creator(idUser)
                    .createTime(now)
                    .build();
                fileEntities.add(build);
            } catch (IOException e) {
                log.info("==============》文件上传失败！{}", e.getMessage());
                throw new ServiceException("文件上传失败:" + e.getMessage());
            }
        }
        this.saveBatch(fileEntities);
        return fileEntities;
    }

    @Override
    public void download(HttpServletResponse response, String identity, String idAttachment) {
        Attachment attachment = this.getById(idAttachment);
        if (ObjectUtil.isEmpty(attachment)) {
            throw new ServiceException("未查询到附件信息");
        }
        try {
            OutputStream os = response.getOutputStream();
            ftpUtil.download(attachment.getPath(),identity,idAttachment, os);
            os.flush();
            os.close();
        } catch (IOException e) {
            log.info("==============》文件下载失败！{}", e.getMessage());
            throw new ServiceException("文件下载失败:" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Boolean del(String idAttachment) {
        Attachment attachment = this.getById(idAttachment);
        if (ObjectUtil.isEmpty(attachment)) {
            throw new ServiceException("未查询到附件信息");
        }
        ftpUtil.delete(attachment.getPath(),attachment.getName());
        return this.removeById(idAttachment);
    }

    @Override
    public void downloadZip(HttpServletResponse response,List<String> ids) {
        String zipFileName = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        response.setContentType("application/zip");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", String.format("attachment;filename=%s.zip",zipFileName));
        List<Attachment> attachmentList = this.listByIds(ids);
        InputStream[] inputStreams = attachmentList.stream().filter(info -> ObjectUtil.isNotEmpty(info.getPath())).map(info -> ftpUtil.download(info.getPath(),info.getIdAttachment())).toArray(InputStream[]::new);
        String[] fileNames = attachmentList.stream().filter(info -> ObjectUtil.isNotEmpty(info.getPath())).map(info -> String.format("%s%s", info.getIdAttachment(), StringUtils.startsWith(info.getSuffix(), ".") ? info.getSuffix() : ("." + info.getSuffix()))).toArray(String[]::new);
        try {
            ZipUtil.zip(response.getOutputStream(), fileNames, inputStreams);
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private String getResourceType(String resourceCode){
        try {
            return resourceCode.substring(0,3);
        } catch (Exception e) {
            throw new ServiceException("资源分类获取错误！请提供正确的资源编码");
        }
    }
}
