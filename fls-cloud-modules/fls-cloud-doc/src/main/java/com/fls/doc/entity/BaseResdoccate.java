package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 资源资料分类表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-17
 */

@Data
@TableName("t_base_resdoccate")
public class BaseResdoccate {
	/**
	* 主键
	*/
	@TableId
	private String idResdoccate;

	/**
	* 资源主键
	*/
	private String idResource;

	/**
	* 资料分类主键
	*/
	private String idDoccate;

	/**
	* 开启引用标识，0=否，1，是，默认0，参见yesorno
	*/
	private String quoteFlag;

	/**
	* 状态，1=未启用，2=已启用，3=已停用，参见basestatus
	*/
	private String status;

	/**
	* 创建时间
	*/
	private Date createTime;

	/**
	* 创建人
	*/
	private String creator;

	/**
	* 作废时间
	*/
	private Date invalidTime;

	/**
	* 作废人
	*/
	private String invalider;

	/**
	* 时间戳
	*/
	private Date ts;

	/**
	* 是否删除，默认0，参见yesorno
	*/
	private String deleteFlag;

	/**
	* 资源编码
	*/
	private String resourceCode;

}
