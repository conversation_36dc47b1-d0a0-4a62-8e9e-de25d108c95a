package com.fls.doc.utils;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.extra.ftp.Ftp;
import cn.hutool.extra.ftp.FtpMode;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.config.FtpAuthority;
import com.fls.doc.config.FtpAuthorityConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FtpUtil {

    private final FtpAuthorityConfig config;

    /**
     * 连接FTP服务端
     */
    public Ftp connect(String identity) {
        FtpAuthority ftpAuthority = config.getFtpAuthorityConfig(identity);
        Ftp ftp = new Ftp(ftpAuthority.getHost(), ftpAuthority.getPort(), ftpAuthority.getUsername(), ftpAuthority.getPassword());
        //设置FTP连接模式
        ftp.setMode(FtpMode.Passive);
        return ftp;
    }

    public String getPath(String identity) {
        FtpAuthority ftpAuthority = config.getFtpAuthorityConfig(identity);
        if (ftpAuthority == null) {
            throw new ServiceException("FTP配置不存在！");
        }
        return ftpAuthority.getPath();
    }

    /**
     * 断开连接
     */
    public void disconnect(Ftp ftp) {
        if (ftp != null) {
            try {
                ftp.close();
            } catch (IOException e) {
                log.info("FTP断开连接失败：{}", e.getMessage());
                throw new ServiceException("FTP断开连接失败：" + e.getMessage());
            }
        }
    }

    /**
     * 上传文件
     *
     * @param path        路径
     * @param fileName    文件名
     * @param inputStream 输入流
     */
    public void upload(String path, String identity, String fileName, InputStream inputStream) throws IOException {
        Ftp ftp = connect(identity);
        log.info(">>>>>>>>>FTP上传>>>>>>>路径：{},文件名：{}", path, fileName);
        try {
            //上传文件
            ftp.upload(path, fileName, inputStream);
        } catch (Exception e) {
            log.error(">>>>>>>>>FTP上传失败>>>>>>>", e);
            throw new ServiceException(e.getMessage());
        } finally {
            inputStream.close();
            disconnect(ftp);
        }
    }

    /**
     * 下载文件
     *
     * @param path         路径
     * @param fileName     文件名
     * @param outputStream 文件流
     */
    public void download(String path, String identity, String fileName, OutputStream outputStream) throws IOException {
        Ftp ftp = connect(identity);
        log.info(">>>>>>>>>FTP下载开始>>>>>>>路径：{},isDir：{},文件名：{}", path, ftp.isDir(path), fileName);
        //下载文件
        try {
            ftp.download(path, fileName, outputStream);
            log.info(">>>>>>>>>FTP下载成功>>>>>>>");
        } catch (Exception e) {
            log.error(">>>>>>>>>FTP下载失败>>>>>>>", e);
            throw new ServiceException(e.getMessage());
        } finally {
            outputStream.close();
            disconnect(ftp);
        }
    }

    public InputStream download(String path, String fileName) {
        FtpAuthority ftpAuthority = config.getFtpAuthorityConfig(null);
        FTPClient ftp = new FTPClient();
        //下载文件
        InputStream inputStream;
        try {
            ftp.connect(ftpAuthority.getHost(), ftpAuthority.getPort());
            ftp.login(ftpAuthority.getUsername(), ftpAuthority.getPassword());
            ftp.changeWorkingDirectory(path);
            ftp.enterLocalPassiveMode();
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            inputStream = ftp.retrieveFileStream(fileName);
            ftp.disconnect();
        } catch (Exception e) {
            log.error(">>>>>>>>>FTP文件获取失败>>>>>>>", e);
            throw new ServiceException(e.getMessage());
        }
        return inputStream;
    }

    /**
     * 删除文件
     *
     * @param path           目录
     * @param remoteFileName 文件名
     * @return 删除状态
     */
    public boolean delete(String path, String remoteFileName) {
        boolean flag;
        Ftp ftp = connect(null);
        //删除文件
        try {
            flag = ftp.delFile(path + "/" + remoteFileName);
        } catch (IORuntimeException e) {
            log.error(">>>>>>>>>FTP删除失败>>>>>>>", e);
            throw new ServiceException(e.getMessage());
        } finally {
            disconnect(ftp);
        }
        return flag;
    }

}
