package com.fls.doc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.BaseRescode;
import com.fls.doc.entity.BaseResource;
import com.fls.doc.mapper.BaseRescodeMapper;
import com.fls.doc.service.BaseRescodeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.doc.service.BaseResourceService;
import com.fls.doc.utils.GetAutoCodeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 资源编码表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Service
@RequiredArgsConstructor
public class BaseRescodeServiceImpl extends ServiceImpl<BaseRescodeMapper, BaseRescode> implements BaseRescodeService {
    private final BaseResourceService resourceService;
    @Override
    public BaseRescode getRescodeByCode(String code) {
        BaseResource resource = resourceService.getResourceByCode(code);
        return this.getOne(new LambdaQueryWrapper<BaseRescode>().eq(BaseRescode::getIdResource,resource.getIdResource()).last("limit 1"));
    }
    @Override
    public String getBillCode(String resoureCode){
        BaseRescode rescode=this.getRescodeByCode(resoureCode);
        if (ObjectUtil.isEmpty(rescode)) {
            throw new ServiceException("资源编码["+resoureCode+"]未配置资源编码表，请检查");
        }
        String flowNumber= GetAutoCodeUtil.getFlowNumber(rescode.getPrefix(), rescode.getFlowNum(), rescode.getLenCode());
        rescode.setFlowNum(rescode.getFlowNum()+1);
        this.updateById(rescode);
        return flowNumber;
    }
}
