package com.fls.doc.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料分类的额外参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_lease_document_parameters")
public class LeaseDocumentParameters implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField("id_addition_parem")
    private String idAdditionParem;

    /**
     * 分类id
     */
    @TableField("id_doc_cat")
    private String idDocCat;

    /**
     * 参数标识
     */
    @TableField("ename")
    private String ename;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 数据类型
     */
    @TableField("valstr")
    private String valstr;

    /**
     * 输入长度
     */
    @TableField("vallen")
    private Integer vallen;

    /**
     * 精度
     */
    @TableField("valacc")
    private Integer valacc;

    /**
     * 1：必选
     */
    @TableField("seltive_flag")
    private String seltiveFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JSONField(serialize = false)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    @JSONField(serialize = false)
    private String creator;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    @JSONField(serialize = false)
    private Date disableTime;

    /**
     * 1:删除
     */
    @TableField("delete_flag")
    @JSONField(serialize = false)
    private String deleteFlag;

    /**
     * 时间戳
     */
    @TableField("ts")
    @JSONField(serialize = false)
    private Date ts;

    /**
     * 1:未启用，2：已启用，3：已停用
     */
    @TableField("status")
    private String status;

    /**
     * 作废时间
     */
    @TableField("invalid_time")
    @JSONField(serialize = false)
    private Date invalidTime;

    /**
     * 作废人
     */
    @TableField("invalider")
    @JSONField(serialize = false)
    private String invalider;

    /**
     * 自定义序号,与资料分类构成联合唯一键
     */
    @TableField("serial_num")
    private String serialNum;
}
