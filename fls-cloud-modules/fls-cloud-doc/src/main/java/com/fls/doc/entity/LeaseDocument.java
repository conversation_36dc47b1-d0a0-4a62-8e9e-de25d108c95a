package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_lease_document")
public class LeaseDocument implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("id_doc")
    private String idDoc;

    /**
     * 资料类型
     */
    @TableField("id_doc_type")
    private String idDocType;

    /**
     * 资料分类
     */
    @TableField("id_doc_cat")
    private String idDocCat;

    /**
     * 所属公司
     */
    @TableField("id_org")
    private String idOrg;

    /**
     * 所属主体
     */
    @TableField("id_bizunit")
    private String idBizunit;

    /**
     * 来源单据类型，id_resource
     */
    @TableField("id_resource")
    private String idResource;

    /**
     * 来源单据号
     */
    @TableField("resource_bill_code")
    private String resourceBillCode;

    /**
     * 实体ID
     */
    @TableField("entity_id")
    private String entityId;

    /**
     * 实体明细ID
     */
    @TableField("entity_detail_id")
    private String entityDetailId;

    /**
     * 资源实体对象名称， 一般为单号
     */
    @TableField("entity_name")
    private String entityName;

    /**
     * 资产id 新
     */
    @TableField("id_assets")
    private String idAssets;

    /**
     * 资料名称
     */
    @TableField("ld_name")
    private String ldName;

    /**
     * 保管公司（id_org）
     */
    @TableField("id_in_company")
    private String idInCompany;

    /**
     * 保管主体
     */
    @TableField("keeping_bizunit")
    private String keepingBizunit;

    /**
     * 责任人id_person
     */
    @TableField("id_person")
    private String idPerson;

    /**
     * 存放地点
     */
    @TableField("ld_place")
    private String ldPlace;

    /**
     * 附件关联id
     */
    @TableField("id_link")
    private String idLink;

    /**
     * 有效时间开始时间
     */
    @TableField("begin_time")
    private String beginTime;

    /**
     * 有效时间结束时间
     */
    @TableField("end_time")
    private String endTime;

    /**
     * 备注
     */
    @TableField("ld_remark")
    private String ldRemark;

    /**
     * -1: 未生效 0：正常，1：丢失，2：损毁
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 1:删除
     */
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

    /**
     * 资料申请状态
     */
    @TableField("bill_status")
    private String billStatus;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("archives_status")
    private String archivesStatus;

    /**
     * 资料编码
     */
    @TableField("ld_file_code")
    private String ldFileCode;

    /**
     * 查阅状态，1：已查阅，0：未查阅
     */
    @TableField("chack_flag")
    private String chackFlag;

    /**
     * 父资料id
     */
    @TableField("parent_id_doc")
    private String parentIdDoc;
}
