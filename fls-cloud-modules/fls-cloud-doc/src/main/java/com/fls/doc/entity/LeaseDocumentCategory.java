package com.fls.doc.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料分类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_lease_document_category")
public class LeaseDocumentCategory implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("id_doc_cat")
    private String idDocCat;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("dc_name")
    private String dcName;

    /**
     * 资产类型
     */
    @TableField("id_doc_type")
    private String idDocType;

    /**
     * 资料引用所属资源
     */
    @TableField("id_resource")
    private String idResource;

    /**
     * 是否启用资料维护申请
     */
    @TableField("apply_flag")
    private String applyFlag;

    /**
     * 是否启用审批
     */
    @TableField("needproc_flag")
    private String needprocFlag;

    /**
     * 资料维护申请资源（默认固定）
     */
    @TableField("id_resource_apply")
    private String idResourceApply;

    /**
     * 资料维护申请资源交易类型
     */
    @TableField("id_res_trantype_apply")
    private String idResTrantypeApply;

    /**
     * 是否启用引用功能
     */
    @TableField("quote_flag")
    private String quoteFlag;

    /**
     * 是否启用有效时间
     */
    @TableField("activetime_flag")
    private String activetimeFlag;

    /**
     * 备注
     */
    @TableField("dc_remark")
    private String dcRemark;

    /**
     * 显示顺序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JSONField(serialize = false)
    private Date createTime;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 创建人
     */
    @TableField("creator")
    @JSONField(serialize = false)
    private String creator;

    /**
     * 时间戳
     */
    @TableField("ts")
    @JSONField(serialize = false)
    private Date ts;

    /**
     * 1：删除
     */
    @TableField("delete_flag")
    @TableLogic
    @JSONField(serialize = false)
    private String deleteFlag;

    /**
     * 1：未启用，2：已启用，3：已停用
     */
    @TableField("status")
    private String status;

    /**
     * 菜单id
     */
    @TableField("id_auth")
    private String idAuth;

    /**
     * 菜单状态
     */
    @TableField("auth_status")
    private String authStatus;

    @TableField(exist = false)
    private String docTypeName;
}
