package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料类型
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_lease_document_type")
public class LeaseDocumentType implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("id_doc_type")
    private String idDocType;

    /**
     * 编码
     */
    @TableField("dt_code")
    private String dtCode;

    /**
     * 名称
     */
    @TableField("dt_name")
    private String dtName;
    @TableField("create_time")
    private Date createTime;
    @TableField("creator")
    private String creator;
    @TableField("disable_time")
    private Date disableTime;
    @TableField("ts")
    private Date ts;
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

    /**
     * 1:开启,0：关闭
     */
    @TableField("activetime_flag")
    private String activetimeFlag;

    /**
     * 1:未启用，2：已启用，3：已停用
     */
    @TableField("status")
    private String status;

    /**
     * 作废时间
     */
    @TableField("invalid_time")
    private Date invalidTime;

    /**
     * 作废人
     */
    @TableField("invalider")
    private String invalider;

    /**
     * 权限id
     */
    @TableField("id_auth")
    private String idAuth;

    /**
     * 菜单状态
     */
    @TableField("auth_status")
    private String authStatus;
}
