package com.fls.doc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.LeaseDocumentAnnex;
import com.fls.doc.entity.LeaseDocumentParameters;
import com.fls.doc.mapper.LeaseDocumentAnnexMapper;
import com.fls.doc.param.DocumentAnnexParam;
import com.fls.doc.service.LeaseDocumentAnnexService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.doc.service.LeaseDocumentParametersService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * LA资料附加参数表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Service
@RequiredArgsConstructor
public class LeaseDocumentAnnexServiceImpl extends ServiceImpl<LeaseDocumentAnnexMapper, LeaseDocumentAnnex> implements LeaseDocumentAnnexService {
    private final LeaseDocumentParametersService parametersService;

    @Override
    public Boolean add(DocumentAnnexParam param) {
        return this.baseMapper.saveAnnex(param);
    }

    @Override
    public List<DocumentAnnexParam> getAnnexParams(String idDoc, String idDocCat) {
        List<LeaseDocumentParameters> parameters = parametersService.list(new MPJLambdaWrapper<LeaseDocumentParameters>().eq(LeaseDocumentParameters::getIdDocCat, idDocCat));
        List<DocumentAnnexParam> annexParams = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(parameters)) {
            if (ObjectUtil.isNotEmpty(parameters)) {
                for (LeaseDocumentParameters parameter : parameters) {
                    String valTable = String.format("%s%s","val",parameter.getSerialNum());
                    try {
                        DocumentAnnexParam annex = this.baseMapper.selectAnnex(idDoc, idDocCat, valTable);
                        if(ObjectUtil.isNotEmpty(annex)){
                            annex.setIdAdditionParem(parameter.getIdAdditionParem());
                            annex.setParemName(parameter.getName());
                            annexParams.add(annex);
                        }
                    } catch (Exception e) {
                        throw new ServiceException(e.getCause().getMessage());
                    }
                }
            }
        }
        return annexParams;
    }
}
