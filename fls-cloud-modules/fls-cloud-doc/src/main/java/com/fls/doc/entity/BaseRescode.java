package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资源编码表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_rescode")
public class BaseRescode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id_rescode")
    private String idRescode;

    /**
     * 资源主键
     */
    @TableField("id_resource")
    private String idResource;

    /**
     * 资源编码
     */
    @TableField("resource_code")
    private String resourceCode;

    /**
     * 前缀名
     */
    @TableField("prefix")
    private String prefix;

    /**
     * 编码长度
     */
    @TableField("len_code")
    private Short lenCode;

    /**
     * 流水号
     */
    @TableField("flow_num")
    private Integer flowNum;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    private String deleteFlag;
}
