package com.fls.doc.utils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalConfing {
    @Value("${approval.url}")
    private String url;
    @Value("${approval.resource-code.doc-code}")
    private String resourceCodeDoc;
    @Value("${approval.resource-code.doc-apply-code}")
    private String resourceCodeDocApply;
}
