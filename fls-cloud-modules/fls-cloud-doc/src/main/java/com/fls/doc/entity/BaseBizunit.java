package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 经营主体表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_bizunit")
public class BaseBizunit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id_bizunit")
    private String idBizunit;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 显示顺序
     */
    @TableField("display_order")
    private Integer displayOrder;

    /**
     * 内部编号
     */
    @TableField("innercode")
    private String innercode;

    /**
     * 经营主体类型 1=终端，2=批发，3=外贸 参见bizunit_type
     */
    @TableField("bizunit_type")
    private String bizunitType;

    /**
     * 开始日期
     */
    @TableField("begin_date")
    private Date beginDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 所属集团
     */
    @TableField("id_group")
    private String idGroup;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    private String deleteFlag;

    /**
     * 核算归属主体
     */
    @TableField("id_accunit")
    private String idAccunit;

    /**
     * 所属组织
     */
    @TableField("id_org")
    private String idOrg;

    /**
     * 所属地区名称
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 经理
     */
    @TableField("id_manager")
    private String idManager;

    /**
     * 维修经理
     */
    @TableField("id_manager_maintenance")
    private String idManagerMaintenance;

    /**
     * 自检登记机关 checking_city_dep
     */
    @TableField("checking_city_dep")
    private String checkingCityDep;

    /**
     * 社会信用代码
     */
    @TableField("appunitcode")
    private String appunitcode;

    /**
     * 资料关联id
     */
    @TableField("id_link_doc")
    private String idLinkDoc;

    /**
     * 单据编码，没用的值，仅用于资料上传不报错
     */
    @TableField("bill_code")
    private String billCode;

    /**
     * 上牌单位邮政编码
     */
    @TableField("usecompostcode")
    private String usecompostcode;

    /**
     * 上牌单位地址
     */
    @TableField("usecomplace")
    private String usecomplace;

    /**
     * 上牌单位固定电话
     */
    @TableField("usecomtel")
    private String usecomtel;

    /**
     * 上牌单位地址行政编码
     */
    @TableField("usecomregion")
    private String usecomregion;

    /**
     * 安全管理员
     */
    @TableField("check_security")
    private String checkSecurity;

    /**
     * 安全管理员电话
     */
    @TableField("check_security_tel")
    private String checkSecurityTel;

    /**
     * 自检人员
     */
    @TableField("check_self")
    private String checkSelf;
}
