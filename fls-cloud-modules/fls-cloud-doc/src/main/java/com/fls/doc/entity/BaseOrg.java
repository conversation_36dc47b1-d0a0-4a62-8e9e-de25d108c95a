package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 组织表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_org")
public class BaseOrg implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织主键
     */
    @TableId("id_org")
    private String idOrg;

    /**
     * 组织编码
     */
    @TableField("code")
    private String code;

    /**
     * 内部编码
     */
    @TableField("innercode")
    private String innercode;

    /**
     * 组织名称
     */
    @TableField("name")
    private String name;

    /**
     * 组织简称
     */
    @TableField("shortname")
    private String shortname;

    /**
     * NC组织pk值
     */
    @TableField("pk_org")
    private String pkOrg;

    /**
     * 是否采购 0=否，1=是 参见yesorno
     */
    @TableField("purchase_flag")
    private String purchaseFlag;

    /**
     * 是否销售 0=否，1=是 参见yesorno
     */
    @TableField("sales_flag")
    private String salesFlag;

    /**
     * 是否物流 0=否，1=是 参见yesorno
     */
    @TableField("traffic_flag")
    private String trafficFlag;

    /**
     * 是否财务 0=否，1=是 参见yesorno
     */
    @TableField("finance_flag")
    private String financeFlag;

    /**
     * 是否库存 0=否，1=是 参见yesorno
     */
    @TableField("stock_flag")
    private String stockFlag;

    /**
     * 是否人力资源 0=否，1=是 参见yesorno
     */
    @TableField("hr_flag")
    private String hrFlag;

    /**
     * 是否行政 0=否，1=是 参见yesorno
     */
    @TableField("admin_flag")
    private String adminFlag;

    /**
     * 是否独立法人 0=否，1=是 参见yesorno
     */
    @TableField("company_flag")
    private String companyFlag;

    /**
     * 组织类型 1=分公司，2=子公司，3=孙公司 参见org_type
     */
    @TableField("org_type")
    private String orgType;

    /**
     * 父级组织
     */
    @TableField("id_parentorg")
    private String idParentorg;

    /**
     * 负责人
     */
    @TableField("org_manager")
    private String orgManager;

    /**
     * 分管领导
     */
    @TableField("org_leader")
    private String orgLeader;

    /**
     * 经纬度
     */
    @TableField("lat_long_alt")
    private String latLongAlt;

    /**
     * 所属集团
     */
    @TableField("id_group")
    private String idGroup;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    private String deleteFlag;

    /**
     * 开票组织
     */
    @TableField("id_org_invoice")
    private String idOrgInvoice;

    /**
     * 注销标识
     */
    @TableField("cancel_flag")
    private String cancelFlag;
}
