package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资源分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_base_resourceclass")
public class BaseResourceclass implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id_resourceclass")
    private String idResourceclass;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 级次
     */
    @TableField("grade")
    private Integer grade;

    /**
     * 上级分类
     */
    @TableField("id_parentclass")
    private String idParentclass;

    /**
     * 是否末级 0=否，1=是 参见yesorno
     */
    @TableField("end_flag")
    private String endFlag;

    /**
     * 启用状态：1=未启用，2=已启用，3=已停用，默认1，参见basestatus
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 时间戳
     */
    @TableField("ts")
    private Date ts;

    /**
     * 是否删除：0=否，1=是，默认0，参见yesorno
     */
    @TableField("delete_flag")
    private String deleteFlag;

    /**
     * 应用工程名
     */
    @TableField("project_name")
    private String projectName;
}
