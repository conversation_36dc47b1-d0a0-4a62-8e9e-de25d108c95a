package com.fls.doc.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料验证器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_lease_document_validator")
public class LeaseDocumentValidator implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("id_validator")
    private String idValidator;
    @TableField("id_doc_cat")
    private String idDocCat;

    /**
     * 所属组织是否必填
     */
    @TableField("id_org_require_flag")
    private String idOrgRequireFlag;

    /**
     * 所属主体是否必填
     */
    @TableField("id_bizunit_require_flag")
    private String idBizunitRequireFlag;

    /**
     * 保管组织是否必填
     */
    @TableField("id_org_keep_require_flag")
    private String idOrgKeepRequireFlag;

    /**
     * 保管主体是否必填
     */
    @TableField("id_bizunit_keep_require_flag")
    private String idBizunitKeepRequireFlag;

    /**
     * 存放地址是否必填
     */
    @TableField("place_require_flag")
    private String placeRequireFlag;

    /**
     * 责任人是否必填
     */
    @TableField("id_person_require_flag")
    private String idPersonRequireFlag;

    /**
     * 创建人
     */
    @TableField("creator")
    @JSONField(serialize = false)
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JSONField(serialize = false)
    private Date createTime;

    /**
     * 失效时间
     */
    @TableField("disable_time")
    private Date disableTime;

    /**
     * 时间戳
     */
    @TableField("ts")
    @JSONField(serialize = false)
    private Date ts;

    /**
     * 1:删除
     */
    @TableField("delete_flag")
    @JSONField(serialize = false)
    private String deleteFlag;
}
