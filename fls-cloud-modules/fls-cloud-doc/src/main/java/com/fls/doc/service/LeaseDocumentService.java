package com.fls.doc.service;

import com.fls.doc.entity.LeaseDocument;
import com.fls.doc.param.DocumentAddParam;
import com.fls.doc.result.DocAddBatchResult;
import com.github.yulichang.base.MPJBaseService;

import java.util.List;

/**
 * <p>
 * LA资料列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
public interface LeaseDocumentService extends MPJBaseService<LeaseDocument> {
    /**
     * 新增资料
     * @param params 参数
     * @return 新增状态
     */
    DocAddBatchResult saveBatch(List<DocumentAddParam> params);
}
