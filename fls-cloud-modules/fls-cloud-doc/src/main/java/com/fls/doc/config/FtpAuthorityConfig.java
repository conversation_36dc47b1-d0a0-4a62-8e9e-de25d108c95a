package com.fls.doc.config;

import com.fls.common.core.exception.ServiceException;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/1/9 10:27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ftp")
public class FtpAuthorityConfig {

    private Map<String, FtpAuthority> auths;

    private final static String DEFAULT_IDENTITY = "master";

    public FtpAuthority getFtpAuthorityConfig(String identity) {
        String key = Optional.ofNullable(identity).orElse(DEFAULT_IDENTITY);
        return Optional.ofNullable(auths.get(key))
            .orElseThrow(() -> new ServiceException("FTP配置不存在！"));
    }
}
