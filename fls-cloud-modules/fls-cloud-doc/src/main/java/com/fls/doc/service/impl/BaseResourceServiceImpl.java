package com.fls.doc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.BaseResource;
import com.fls.doc.mapper.BaseResourceMapper;
import com.fls.doc.service.BaseResourceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 资源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Service
public class BaseResourceServiceImpl extends ServiceImpl<BaseResourceMapper, BaseResource> implements BaseResourceService {

    @Override
    public BaseResource getResourceByCode(String code) {
        BaseResource resource = this.getOne(new LambdaQueryWrapper<BaseResource>().eq(BaseResource::getCode, code).last("limit 1"));
        if (ObjectUtil.isEmpty(resource)) {
            throw new ServiceException(String.format("未查询到[%s]资源资源信息！",code));
        }
        return resource;
    }
}
