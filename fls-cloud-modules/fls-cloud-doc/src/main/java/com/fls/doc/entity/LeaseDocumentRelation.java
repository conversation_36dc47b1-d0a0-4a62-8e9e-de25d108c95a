package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_lease_document_relation")
public class LeaseDocumentRelation implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId("id_doc_rel")
    private String idDocRel;

    /**
     * 关系值
     */
    @TableField("id_link_doc")
    private String idLinkDoc;
    @TableField("id_doc")
    private String idDoc;

    /**
     * 资料分类
     */
    @TableField("id_doc_cat")
    private String idDocCat;

    /**
     * 引用标识
     */
    @TableField("quote_flag")
    private String quoteFlag;

    /**
     * 最新标识
     */
    @TableField("new_flag")
    private String newFlag;
    @TableField("ts")
    private Date ts;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 作废标识
     */
    @TableField("invalid_flag")
    private String invalidFlag;

    /**
     * 作废人
     */
    @TableField("invalider")
    private String invalider;

    /**
     * 作废时间
     */
    @TableField("invalid_time")
    private Date invalidTime;

    /**
     * 1:删除
     */
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;

    /**
     * 上级资料id
     */
    @TableField("id_link_doc_parent")
    private String idLinkDocParent;
    @TableField("init_flag")
    private String initFlag;
}
