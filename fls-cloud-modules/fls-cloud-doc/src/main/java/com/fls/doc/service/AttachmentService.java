package com.fls.doc.service;

import com.fls.doc.entity.Attachment;
import com.github.yulichang.base.MPJBaseService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 附件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
public interface AttachmentService extends MPJBaseService<Attachment> {
    /**
     * 上传附件
     * @param files 文件列表
     * @param idResource 资源id
     * @param idLink 附件链接id
     * @return 附件列表
     */
    List<Attachment> uploads(MultipartFile[] files, String identity, String idResource, String idLink, String idUser);

    /**
     * 下载附件
     * @param response 响应
     * @param identity 系统标识
     * @param idAttachment 附件id
     */
    void download(HttpServletResponse response, String identity, String idAttachment);

    /**
     *
     * @param idAttachment 附件id
     * @return 删除结果
     */
    Boolean del(String idAttachment);

    void downloadZip(HttpServletResponse response,List<String> ids);
}
