package com.fls.doc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * LA资料交接
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_lease_document_handover")
public class LeaseDocumentHandover implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId("id_document_handover")
    private String idDocumentHandover;

    /**
     * 资料分类id
     */
    @TableField("id_doc_cat")
    private String idDocCat;

    /**
     * 资料ID
     */
    @TableField("id_doc")
    private String idDoc;

    /**
     * 经营主体id
     */
    @TableField("id_org")
    private String idOrg;

    /**
     * 责任人
     */
    @TableField("person_id")
    private String personId;

    /**
     * 备注
     */
    @TableField("ldh_remark")
    private String ldhRemark;

    /**
     * 交接时间
     */
    @TableField("ldh_handover_time")
    private Date ldhHandoverTime;

    /**
     * 是否签收
     */
    @TableField("ldh_signed")
    private String ldhSigned;

    /**
     * 签收时间
     */
    @TableField("ldh_signin_time")
    private Date ldhSigninTime;

    /**
     * 上一责任人
     */
    @TableField("ldh_previous")
    private String ldhPrevious;
    @TableField("create_time")
    private Date createTime;
    @TableField("creator")
    private String creator;
    @TableField("disable_time")
    private Date disableTime;
    @TableField("ts")
    private String ts;
    @TableField("delete_flag")
    @TableLogic
    private String deleteFlag;
}
