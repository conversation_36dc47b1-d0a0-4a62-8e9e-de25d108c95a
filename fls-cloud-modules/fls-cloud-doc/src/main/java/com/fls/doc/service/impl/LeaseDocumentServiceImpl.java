package com.fls.doc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.enums.FlagEnum;
import com.fls.common.core.exception.ServiceException;
import com.fls.doc.entity.*;
import com.fls.doc.enums.DocStatusEnum;
import com.fls.doc.mapper.LeaseDocumentMapper;
import com.fls.doc.param.DocumentAddParam;
import com.fls.doc.param.DocumentAnnexParam;
import com.fls.doc.result.DocAddBatchResult;
import com.fls.doc.service.*;
import com.fls.doc.utils.ApprovalConfing;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * LA资料列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-10
 */
@Service
@RequiredArgsConstructor
public class LeaseDocumentServiceImpl extends MPJBaseServiceImpl<LeaseDocumentMapper, LeaseDocument> implements LeaseDocumentService {
    private final LeaseDocumentCategoryService categoryService;
    private final LeaseDocumentValidatorService validatorService;
    private final LeaseDocumentParametersService parametersService;
    private final LeaseDocumentDatamaintainanceService datamaintainanceService;
    private final LeaseDocumentRelationService relationService;
    private final LeaseDocumentAnnexService annexService;
    private final BaseRescodeService rescodeService;
    private final ApprovalConfing approvalConfing;
    @Value("${document.category.default-validator-id}")
    private String defaultValidatorId;
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public DocAddBatchResult saveBatch(@NotNull List<DocumentAddParam> params) {
        DocAddBatchResult result=new DocAddBatchResult();
        String idLinkDoc = IdUtil.randomUUID();
        for (DocumentAddParam param : params) {
            //如果传参不为空，则idLinkDoc等于传参值
            if (ObjectUtil.isNotEmpty(param.getIdLinkDoc())) {
                idLinkDoc=param.getIdLinkDoc();
            }
            //如果是引用资料，添加资料关系，跳出当前循坏
            if (param.getQuoteFlag().equals(FlagEnum.YES.getCode())) {
                if (ObjectUtil.isEmpty(param.getQuoteIdDoc())) {
                    throw new ServiceException("引用档案id不能为空");
                }
                LeaseDocumentRelation documentRelation = relationService.getOne(new MPJLambdaWrapper<LeaseDocumentRelation>().eq(LeaseDocumentRelation::getQuoteFlag, FlagEnum.NOT.getCode()).last("limit 1"));
                if (ObjectUtil.isEmpty(documentRelation)) {
                    throw new ServiceException("未查询到有效资料关系！");
                }
                LeaseDocument document = this.getById(param.getQuoteIdDoc());
                LeaseDocumentRelation relation = new LeaseDocumentRelation();
                relation.setIdDoc(document.getIdDoc());
                relation.setIdDocCat(document.getIdDocCat());
                relation.setIdLinkDoc(idLinkDoc);
                relation.setIdLinkDocParent(documentRelation.getIdLinkDoc());
                relation.setCreator(param.getIdUser());
                relationService.save(relation);
                continue;
            }
            //查询资料分类信息
            LeaseDocumentCategory documentCategory = categoryService.getById(param.getIdDocCat());
            if (ObjectUtil.isEmpty(documentCategory)) {
                throw new ServiceException("未查询到资料分类信息，请检查！");
            }
            //校验档案分类必填项
            documentCategoryValidator(param,documentCategory);
            //填充保存资料档案
            LeaseDocument document = BeanUtil.copyProperties(param, LeaseDocument.class);
            document.setIdDoc(IdUtil.randomUUID());
            document.setLdFileCode(rescodeService.getBillCode(approvalConfing.getResourceCodeDoc()));
            if (documentCategory.getApplyFlag().equals(FlagEnum.YES.getCode())) {
                document.setStatus(DocStatusEnum.INVALID.getCode());
            } else {
                document.setStatus(DocStatusEnum.NORMAL.getCode());
            }
            document.setBillStatus(CommonConstants.COMMON_STATUS_DISABLED);
            document.setCreator(param.getIdUser());
            this.save(document);
            List<String> datamaintainanceIds=new ArrayList<>();
            //判断资料分类是否启用资料维护申请
            if (FlagEnum.YES.getCode().equals(documentCategory.getApplyFlag())) {
                //填充并保存资料维护申请
                LeaseDocumentDatamaintainance datamaintainance = BeanUtil.copyProperties(document, LeaseDocumentDatamaintainance.class);
                datamaintainance.setIdBillDoc(IdUtil.randomUUID());
                datamaintainance.setBillDate(new Date());
                datamaintainance.setIdResource(documentCategory.getIdResourceApply());
                datamaintainance.setIdResTrantype(documentCategory.getIdResTrantypeApply());
                datamaintainance.setCreator(param.getIdUser());
                datamaintainance.setStatus(CommonConstants.COMMON_STATUS_DISABLED);
                datamaintainance.setBillCode(rescodeService.getBillCode(approvalConfing.getResourceCodeDocApply()));
                datamaintainanceService.save(datamaintainance);
                datamaintainanceIds.add(datamaintainance.getIdBillDoc());
            }
            result.setDatamaintainanceIds(datamaintainanceIds);
            //填充并保存资料附件参数
            if (ObjectUtil.isNotEmpty(param.getAnnexParams())) {
                for (DocumentAnnexParam annexParam : param.getAnnexParams()) {
                    annexParam.setIdAnnex(IdUtil.randomUUID());
                    annexParam.setIdDoc(document.getIdDoc());
                    annexParam.setIdDocCat(documentCategory.getIdDocCat());
                    annexParam.setCreator(param.getIdUser());
                    try {
                        annexService.add(annexParam);
                    } catch (Exception e) {
                        throw new ServiceException(e.getCause().getMessage());
                    }
                }
            }
            //填充资料关系关系
            LeaseDocumentRelation relation = new LeaseDocumentRelation();
            relation.setIdDoc(document.getIdDoc());
            relation.setIdDocCat(documentCategory.getIdDocCat());
            relation.setIdLinkDoc(idLinkDoc);
            relation.setCreator(param.getIdUser());
            relation.setNewFlag(FlagEnum.YES.getCode());
            relationService.save(relation);
        }
        result.setIdLinkDoc(idLinkDoc);
        return result;
    }

    /**
     * 校验资料分类必填项
     * @param param 资料参数
     * @param documentCategory 资料分类信息
     */
    public void documentCategoryValidator(DocumentAddParam param,LeaseDocumentCategory documentCategory){
        //判断资料分类检验器
        LeaseDocumentValidator documentValidator = validatorService.getOne(new MPJLambdaWrapper<LeaseDocumentValidator>().eq(LeaseDocumentValidator::getIdDocCat, documentCategory.getIdDocCat()).eq(LeaseDocumentValidator::getDeleteFlag,CommonConstants.DELETE_FLAG_NOT_DELETED).last("limit 1"));
        if (ObjectUtil.isEmpty(documentValidator)) {
            documentValidator=validatorService.getOne(new MPJLambdaWrapper<LeaseDocumentValidator>().eq(LeaseDocumentValidator::getIdDocCat, defaultValidatorId).last("limit 1"));
        }
        if (ObjectUtil.isNotEmpty(documentValidator)) {
            verificationDocumentValidator(param, documentValidator);
        }
        //判断资料分类额外参数
        List<LeaseDocumentParameters> documentParameters = parametersService.list(new MPJLambdaWrapper<LeaseDocumentParameters>().eq(LeaseDocumentParameters::getIdDocCat, documentCategory.getIdDocCat()).eq(LeaseDocumentParameters::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED));
        if (ObjectUtil.isNotEmpty(documentParameters)) {
            if (ObjectUtil.isEmpty(param.getAnnexParams())) {
                throw new ServiceException("缺少资料附件参数[annexParams]");
            }
            for (LeaseDocumentParameters parameter : documentParameters) {
                String idAdditionParem=null;
                for (DocumentAnnexParam annexParam : param.getAnnexParams()) {
                    //如果有相同的参数并且必填但是参数为空的报错
                    if (parameter.getIdAdditionParem().equals(annexParam.getIdAdditionParem())) {
                        idAdditionParem=parameter.getIdAdditionParem();
                        annexParam.setValTable("val"+parameter.getSerialNum());
                        if (parameter.getSeltiveFlag().equals(FlagEnum.YES.getCode()) && ObjectUtil.isEmpty(annexParam.getVal())) {
                            throw new ServiceException(String.format("资料分类额外参数[%s]值必填，请检查",parameter.getName()));
                        }
                    }
                }
                //如果上面没有找到并且这个参数必填报错
                if (ObjectUtil.isEmpty(idAdditionParem)&& parameter.getSeltiveFlag().equals(FlagEnum.YES.getCode())){
                    throw new ServiceException(String.format("缺少资料分类额外必填项[%s]",parameter.getName()));
                }
            }
        }
    }
    /**
     * 校验资料分类校验器
     *
     * @param params    资料参数
     * @param validator 资料分类校验器
     */
    private void verificationDocumentValidator(DocumentAddParam params, LeaseDocumentValidator validator) {
        if(validator.getIdOrgRequireFlag().equals(FlagEnum.YES.getCode())&&ObjectUtil.isEmpty(params.getIdOrg())){
            throw new ServiceException("缺少必填参数[所属组织]");
        }
        if(validator.getIdBizunitRequireFlag().equals(FlagEnum.YES.getCode())&&ObjectUtil.isEmpty(params.getIdBizunit())){
            throw new ServiceException("缺少必填参数[所属主体]");
        }
        if(validator.getIdOrgKeepRequireFlag().equals(FlagEnum.YES.getCode())&&ObjectUtil.isEmpty(params.getIdInCompany())){
            throw new ServiceException("缺少必填参数[保管组织]");
        }
        if(validator.getIdBizunitKeepRequireFlag().equals(FlagEnum.YES.getCode())&&ObjectUtil.isEmpty(params.getKeepingBizunit())){
            throw new ServiceException("缺少必填参数[保管主体]");
        }
        if(validator.getIdPersonRequireFlag().equals(FlagEnum.YES.getCode())&&ObjectUtil.isEmpty(params.getIdPerson())){
            throw new ServiceException("缺少必填参数[责任人]");
        }
        if(validator.getPlaceRequireFlag().equals(FlagEnum.YES.getCode())&&ObjectUtil.isEmpty(params.getLdPlace())){
            throw new ServiceException("缺少必填参数[存放地点]");
        }
    }
}
