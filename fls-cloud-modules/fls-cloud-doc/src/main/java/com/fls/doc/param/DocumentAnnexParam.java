package com.fls.doc.param;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class DocumentAnnexParam {
    private String idAnnex;
    private String paremName;
    private String idAdditionParem;
    @JSONField(serialize = false)
    private String idDoc;
    @JSONField(serialize = false)
    private String idDocCat;
    private String val;
    @JSONField(serialize = false)
    private String valTable;
    @JSONField(serialize = false)
    private String creator;
}
