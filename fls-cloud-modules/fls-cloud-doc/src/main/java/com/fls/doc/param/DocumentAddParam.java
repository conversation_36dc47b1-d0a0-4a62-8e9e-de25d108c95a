package com.fls.doc.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DocumentAddParam {
    /**
     * 资料类型
     */
    @NotEmpty(message = "缺少必填参数[资料类型]")
    private String idDocType;

    /**
     * 资料分类
     */
    @NotEmpty(message = "缺少必填参数[资料分类]")
    private String idDocCat;

    /**
     * 所属公司
     */

    private String idOrg;

    /**
     * 所属主体
     */
    private String idBizunit;

    /**
     * 来源单据类型，id_resource
     */
    //@NotEmpty(message = "缺少必填参数[来源单据类型]")
    private String idResource;

    /**
     * 来源单据号
     */
    //@NotEmpty(message = "缺少必填参数[resourceBillCode]")
    private String resourceBillCode;

    /**
     * 实体ID
     */
    //@NotEmpty(message = "缺少必填参数[entityId]")
    private String entityId;

    /**
     * 实体明细ID
     */
    private String entityDetailId;

    /**
     * 资源实体对象名称， 一般为单号
     */
    //@NotEmpty(message = "缺少必填参数[entityName]")
    private String entityName;

    /**
     * 资料名称
     */
    @NotEmpty(message = "缺少必填参数[资料名称]")
    private String ldName;

    /**
     * 保管公司（id_org）
     */
    private String idInCompany;

    /**
     * 保管主体
     */
    private String keepingBizunit;

    /**
     * 责任人id_person
     */
    private String idPerson;

    /**
     * 存放地点
     */
    private String ldPlace;

    /**
     * 附件关联id
     */
    @NotEmpty(message = "缺少必填参数[附件关联id]")
    private String idLink;

    /**
     * 有效时间开始时间
     */
    private String beginTime;

    /**
     * 有效时间结束时间
     */
    private String endTime;

    /**
     * 备注
     */
    private String ldRemark;

    /**
     * 引用标识
     */
    @NotEmpty(message = "缺少必填参数[引用标识]")
    private String quoteFlag;

    /**
     * 引用档案id
     */
    private String quoteIdDoc;

    /**
     *资料附件参数
     */
    private List<DocumentAnnexParam> annexParams;

    /**
     * 用户id
     */
    @NotEmpty(message = "缺少必填参数[用户id]")
    private String idUser;
    /**
     * 档案链接id
     */
    private String idLinkDoc;
}
