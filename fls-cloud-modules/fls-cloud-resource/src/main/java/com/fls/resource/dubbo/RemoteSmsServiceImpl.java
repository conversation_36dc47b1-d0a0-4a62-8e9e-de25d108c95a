package com.fls.resource.dubbo;

import cn.hutool.core.bean.BeanUtil;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.core.utils.SpringUtils;
import com.fls.common.message.config.properties.SmsProperties;
import com.fls.common.message.core.SmsTemplate;
import com.fls.common.message.entity.SmsResult;
import com.fls.resource.api.RemoteSmsService;
import com.fls.resource.api.domain.SysSms;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 短信服务
 *
 * <AUTHOR>
 * @date 2022/06/28
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteSmsServiceImpl implements RemoteSmsService {

    private final SmsProperties smsProperties;

    /**
     * 发送短信
     *
     * @param phones     电话号(多个逗号分割)
     * @param templateId 模板id
     * @param param      模板对应参数
     */
    @Override
    public SysSms send(String phones, String templateId, Map<String, String> param) throws ServiceException {
        if (smsProperties.getEnabled()) {
            ResponseData.fail("当前系统没有开启短信功能！");
        }
        SmsTemplate smsTemplate = SpringUtils.getBean(SmsTemplate.class);
        SmsResult smsResult = smsTemplate.send(phones, templateId, param);
        return BeanUtil.toBean(smsResult, SysSms.class);
    }

}
