package com.fls.invoice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fls.invoice.config.CloudServiceConfig;
import com.fls.invoice.config.InvoiceConfig;
import com.fls.invoice.constant.SysDictionary;
import com.fls.invoice.entity.ApiThirdLog;
import com.fls.invoice.entity.InvoiceElec;
import com.fls.invoice.entity.InvoiceElecB;
import com.fls.invoice.entity.dto.InvoiceElecDto;
import com.fls.invoice.service.ApiThirdLogService;
import com.fls.invoice.service.InvoiceElecBService;
import com.fls.invoice.service.InvoiceElecService;
import com.fls.invoice.service.InvoiceService;
import com.fls.invoice.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {
    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private CloudServiceConfig cloudServiceConfig;

    @Resource
    private ApiThirdLogService apiThirdLogService;

    @Resource
    private InvoiceElecService invoiceElecService;

    @Resource
    private InvoiceElecBService invoiceElecBService;

    @Resource
    private RestTemplate restTemplate;

    @Override
    public Map<String, Object> push(List<InvoiceElecDto> invoiceList) throws Exception {
        Map<String,Object> map = new HashMap<>();
        List<Map<String, Object>> gdList = new ArrayList<>();
        BigDecimal bd = new BigDecimal(100);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //检查是否已推送过
        for(InvoiceElecDto dto:invoiceList){
            InvoiceElec invoiceElec = invoiceElecService.getInvoiceElec(dto.getBillNo());
            if(invoiceElec!=null){
                map.put("code", SysDictionary.RESULT_FAIL);
                map.put("msg","该单据已推送过["+dto.getBillNo()+"]");
                return map;
            }
        }

        for(InvoiceElecDto dto:invoiceList) {
            Map<String, Object> billMap = new HashMap<>();
            billMap.put("gddjh", dto.getBillNo()); //工单单据号
            billMap.put("gmfMc", dto.getBuyer()); //购买方名称
            billMap.put("gmfNsrsbh", dto.getBuyerTaxid()); //购买方纳税人识别号
            billMap.put("lzrq", sdf.format(dto.getBillDate())); //立账日期
            billMap.put("hsbz", SysDictionary.YESORNO_YES.equals(dto.getTaxFlag())?"Y":"N"); //含税标志(Y-是；N-否)

            List<Map<String, Object>> mxzbList = new ArrayList<>();
            BigDecimal total = new BigDecimal(0);
            StringBuilder sb = new StringBuilder();
            int i=1;
            for(InvoiceElecB invoiceElecB:dto.getbList()){
                //数量为0的不传
                if(invoiceElecB.getNum().compareTo(BigDecimal.ZERO)!=0){
                    Map<String, Object> item = new HashMap<>();
                    item.put("mxxh", i);
                    item.put("lpmc", invoiceElecB.getInvName());
                    item.put("lpbh", invoiceElecB.getInvCode());
                    item.put("spsl", invoiceElecB.getNum());
                    //如果币种是人民币的才传单价，因为金额是人民币的。单价有可能是外币的单据
                    if("CNY".equals(invoiceElecB.getCurrCode())){
                        item.put("dj", invoiceElecB.getPrice());
                        item.put("hsdj", invoiceElecB.getTaxPrice());
                    }
                    item.put("je", invoiceElecB.getMoney());
                    item.put("hsje", invoiceElecB.getTaxMoney());
                    item.put("slv", invoiceElecB.getTaxRate());
                    item.put("dw", invoiceElecB.getUnit());
                    mxzbList.add(item);
                    if(invoiceElecB.getNum()!=null){
                        total = total.add(invoiceElecB.getNum());
                    }
                    if(!StringUtils.isEmpty(invoiceElecB.getInvName())){
                        sb.append(invoiceElecB.getInvName()).append(",");
                    }
                    i++;
                }
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1).toString();
            }
            billMap.put("sl", total); //明细总数量
            billMap.put("lpmc", sb.toString()); //料品名称(明细中料品名称集合)
            billMap.put("mxzbList", mxzbList);

            gdList.add(billMap);
        }

        String seller = invoiceList.get(0).getSeller();
        String url = "";
        String mehtod = "invoice/gd/fpkj/syncGd";
        if(SysDictionary.INVOICE_SELLER_PENGZE.equals(seller)){
            url = invoiceConfig.getPengze().getUrl() + mehtod +"?jrzh="+invoiceConfig.getPengze().getUser()+"&jrmm="+invoiceConfig.getPengze().getPwd()+"&sjhm="+invoiceConfig.getPengze().getMobile()+"&nsrsbh="+invoiceConfig.getPengze().getTaxid();
        }else{
            url = invoiceConfig.getFolangsi().getUrl() + mehtod +"?jrzh="+invoiceConfig.getFolangsi().getUser()+"&jrmm="+invoiceConfig.getFolangsi().getPwd()+"&sjhm="+invoiceConfig.getFolangsi().getMobile()+"&nsrsbh="+invoiceConfig.getFolangsi().getTaxid();
        }

        Map<String, InvoiceElecDto> listmap = invoiceList.stream().collect(Collectors.toMap(InvoiceElecDto::getBillNo, Function.identity()));
        Date now = new Date();
        String ts = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(gdList, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
        log.info("调用慧电票接口，请求参数===>{}",gdList.toString());
        //调用开票接口
        ResponseEntity<String> rep = restTemplate.postForEntity(url, formEntity, String.class);
        String repStr = rep.getBody();
        JSONObject repBoby = JSON.parseObject(repStr);
        log.info("慧电票接口返回===>{}",repBoby.toJSONString());

        boolean isSucc = (Boolean)repBoby.get("success");
        String code = "";
        if(isSucc){
            JSONObject result = (JSONObject)repBoby.get("result");
            code = (String)result.get("code");
            JSONObject data = new JSONObject();
            data.put("code",code);
            //成功列表
            JSONArray succArray=(JSONArray)result.get("gdList");
            List<Map<String,Object>> succList = new ArrayList<>();
            for (int i = 0; i < succArray.size(); i++) {
                JSONObject item = succArray.getJSONObject(i);
                Map<String,Object> itemMap = new HashMap<>();
                itemMap.put("tradeId",(String)item.get("id")); //慧企受理单号
                itemMap.put("billNo",(String)item.get("gddjh")); //工单号
                itemMap.put("status",(String)item.get("clzt")); //处理状态
                succList.add(itemMap);

                //保存本地数据
                InvoiceElec invoiceElec = listmap.get((String)item.get("gddjh"));
                String idInvoiceElec = UUID.randomUUID().toString();
                invoiceElec.setIdInvoiceElec(idInvoiceElec);
                invoiceElec.setStatus(SysDictionary.INVOICE_STATUS_UNDO); //未开票
                invoiceElec.setCreateTime(now);
                invoiceElec.setUpdateTime(now);
                invoiceElec.setTs(ts);
                invoiceElec.setDeleteFlag(SysDictionary.YESORNO_NO);
                invoiceElecService.save(invoiceElec);
                List<InvoiceElecB> bList = listmap.get((String)item.get("gddjh")).getbList();
                for(InvoiceElecB elecB:bList){
                    elecB.setIdInvoiceElecB(UUID.randomUUID().toString());
                    elecB.setIdInvoiceElec(idInvoiceElec);
                    elecB.setCreateTime(now);
                    elecB.setUpdateTime(now);
                    elecB.setTs(ts);
                    elecB.setDeleteFlag(SysDictionary.YESORNO_NO);
                }
                invoiceElecBService.saveBatch(bList);
            }
            //失败列表
            JSONArray errArray=(JSONArray)result.get("errorList");
            List<Map<String,Object>> errList = new ArrayList<>();
            for (int i = 0; i < errArray.size(); i++) {
                JSONObject item = errArray.getJSONObject(i);
                Map<String,Object> itemMap = new HashMap<>();
                itemMap.put("tradeId",(String)item.get("id")); //慧企受理单号
                itemMap.put("billNo",(String)item.get("gddjh")); //工单号
                itemMap.put("status",(String)item.get("clzt")); //处理状态
                errList.add(itemMap);
            }
            data.put("succList",succList);
            data.put("errList",errList);
            map.put("data",data);
            map.put("code",repBoby.get("code"));
            map.put("msg",repBoby.get("message"));

        } else {
            map.put("code",repBoby.get("code"));
            map.put("msg",repBoby.get("message"));
        }

        //记录日志
        String idLog = UUID.randomUUID().toString();
        ApiThirdLog apiThirdLog = new ApiThirdLog();
        apiThirdLog.setIdLog(idLog);
        apiThirdLog.setType("dianpiao");
        apiThirdLog.setUrl(invoiceConfig.getFolangsi().getUrl());
        apiThirdLog.setMethod(mehtod);
        apiThirdLog.setParams(gdList.toString());
        if(isSucc){
            if("0".equals(code)){
                apiThirdLog.setResult("成功");
            }else if("1".equals(code)){
                apiThirdLog.setResult("部分成功");
            }else{
                apiThirdLog.setResult("失败");
            }
        }else{
            apiThirdLog.setResult("失败");
        }
        apiThirdLog.setResponse(repBoby.toJSONString());
        apiThirdLog.setTs(ts);
        apiThirdLogService.save(apiThirdLog);

        return map;
    }

    @Override
    public Map<String, Object> notify(String uri,String method,Map<String, String> paramMap, JSONObject bodyMap) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        //验证参数
        String url = cloudServiceConfig.getDomain() + uri;
        boolean flag = verifyParams(paramMap,url,method);
        if(flag){
            //转换请求体
            JSONObject notifyBody = getNotifyBoby(bodyMap);
            //业务系统url
            JSONArray billNoArr = (JSONArray)notifyBody.get("billNos");
            InvoiceElec invoiceElec = invoiceElecService.getInvoiceElec((String)billNoArr.get(0));
            String bizUrl = invoiceElec.getBizNotifyUrl();
            //执行业务
            log.info("通知业务系统===>{}",notifyBody);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> formEntity = new HttpEntity<String>(JSONObject.toJSONString(notifyBody, SerializerFeature.WriteMapNullValue), headers); //允许传递body中的null值
            ResponseEntity<String> rep = restTemplate.postForEntity(bizUrl, formEntity, String.class);
            String repStr = rep.getBody();
            JSONObject repBoby = JSON.parseObject(repStr);
            log.info("业务系统返回===>{}",repBoby.toJSONString());

            if((Integer)repBoby.get("code") == 200){
                map.put("errorCode", "0");
                map.put("errorMsg", "签收成功");
                map.put("timestamp", System.currentTimeMillis());
            }else{
                map.put("errorCode", "500");
                map.put("errorMsg", repBoby.get("msg"));
                map.put("timestamp", System.currentTimeMillis());
            }

            //更新本地数据
            Map<String,Object> param = new HashMap<>();
            param.put("status", SysDictionary.INVOICE_STATUS_SUCCESS);
            param.put("invoiceRequestId",notifyBody.get("requestId"));
            param.put("invoiceNo", notifyBody.get("invoiceNo"));
            param.put("invoiceTime", notifyBody.get("invoiceTime"));
            param.put("invoiceDownUrl", notifyBody.get("invoiceDownUrl"));
            param.put("pdfDownUrl", notifyBody.get("pdfDownUrl"));
            param.put("billnos", billNoArr);
            invoiceElecService.updateBatchParam(param);
        }else{
            map.put("errorCode", "500");
            map.put("errorMsg", SysDictionary.ERROR_CODE_05_DESC);
            map.put("timestamp", System.currentTimeMillis());
        }
        return map;
    }

    /**
     * 校验传参
     * @param req
     * @return
     */
    private Boolean verifyParams(Map<String, String> req,String path,String reqMethod) {
        log.info("Nonce： {}",req.get("Nonce"));
        log.info("SecretId： {}",req.get("SecretId"));
        log.info("Timestamp： {}",req.get("Timestamp"));
        log.info("Signature： {}",req.get("Signature"));

        String secretId = req.get("SecretId"); //API 调用者身份唯一标识
        String secretKey = invoiceConfig.getFolangsi().getSignkey();// 签名密钥(严格保管,避免泄露)
        TreeMap<String, String> requestParams = new TreeMap<>();
        requestParams.put("SecretId", secretId);//调用者身份唯一标识
        requestParams.put("Timestamp", req.get("Timestamp"));//当前时间戳
        requestParams.put("Nonce", req.get("Nonce"));//随机正整数
        // 1、拼接签名源文字符串
        String s = SignUtil.makeSignPlainText(requestParams, reqMethod, path);
        log.info("签名原文===> {}",s);
        // 2、生成签名串
        String signature = SignUtil.sign(secretKey, s, "HmacSHA1");
        log.info("签名串(Signature)===> {}",signature);
        if(signature.equals(req.get("Signature"))){
            return true;
        }else{
            return false;
        }
    }

    private JSONObject getNotifyBoby(JSONObject bodyMap){
        JSONObject notifyBody = new JSONObject();
        JSONObject srcBody = (JSONObject)bodyMap.get("body");
        notifyBody.put("billNos",srcBody.get("gddjh")); //工单单据号，数组
        notifyBody.put("bizType",srcBody.get("slywlxDm")); //业务类型代码: 01- 蓝字发票开具结果
        notifyBody.put("errCode",srcBody.get("errorCode")); //错误码(仅执行失败时存在)
        notifyBody.put("errMsg",srcBody.get("errorMsg")); //错误信息(仅执行失败时存在)
        notifyBody.put("requestId",srcBody.get("fpqqlsh")); //发票请求流水号
        notifyBody.put("buyerTaxid",srcBody.get("gmfNsrsbh")); //购方税号
        notifyBody.put("invoiceNo",srcBody.get("qdfphm")); //全电发票号码
        notifyBody.put("buyer",srcBody.get("gmfMc")); //购方名称
        notifyBody.put("invoiceDownUrl",srcBody.get("fpxzUrl")); //发票下载地址
        notifyBody.put("pdfDownUrl",srcBody.get("pdfxzUrl")); //发票PDF下载地址
        Date invoiceTime = new Date();
        invoiceTime.setTime((Long)srcBody.get("kprq"));
        notifyBody.put("invoiceTime",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(invoiceTime)); //开票时间
        notifyBody.put("totalTaxMoney",srcBody.get("jshj")); //价税合计(元)
        return notifyBody;
    }
}
