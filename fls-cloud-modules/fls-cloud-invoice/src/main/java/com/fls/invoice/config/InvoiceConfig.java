package com.fls.invoice.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "dianpiao")
@Data
public class InvoiceConfig {
    private Folangsi folangsi = new Folangsi() ;
    private Pengze pengze = new Pengze();

    @Configuration
    @ConfigurationProperties(prefix = "dianpiao.folangsi")
    @Data
    public class Folangsi {
        //接口地址
        private String url;
        //接入账号
        private String user;
        //接入密码
        private String pwd;
        //接入手机号码
        private String mobile;
        //签名密钥
        private String signkey;
        //纳税人识别号
        private String taxid;
    }

    @Configuration
    @ConfigurationProperties(prefix = "dianpiao.pengze")
    @Data
    public class Pengze {
        //接口地址
        private String url;
        //接入账号
        private String user;
        //接入密码
        private String pwd;
        //接入手机号码
        private String mobile;
        //签名密钥
        private String signkey;
        //纳税人识别号
        private String taxid;
    }
}
