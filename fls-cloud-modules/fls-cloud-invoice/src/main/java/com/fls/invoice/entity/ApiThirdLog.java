package com.fls.invoice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_api_third_log")
public class ApiThirdLog {
	@TableId(value = "id_log", type = IdType.ASSIGN_UUID)
	private String idLog; //主键
	private String type; //调用接口系统类别
	private String url; //请求URL
	private String method; //接口方法
	private String params; //请求参数
	private String result; //调用结果
	private String response; //接口返回
	private String ts; //时间戳
}
