package com.fls.invoice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("t_invoice_elec_b")
public class InvoiceElecB {
    @TableId(value = "id_invoice_elec_b", type = IdType.ASSIGN_UUID)
    private String idInvoiceElecB; //主键
    private String idInvoiceElec; //主表主键
    private String invName; //料品名称
    private String invCode; //料品编号
    private String invSpec; //规格型号
    private String invMeasdoc; //料品单位
    private BigDecimal num; //料品数量
    private BigDecimal price; //单价
    private BigDecimal money; //金额
    private BigDecimal taxPrice; //含税单价
    private BigDecimal taxMoney; //含税金额
    private BigDecimal taxRate; //税率
    private BigDecimal tax; //税额
    private String currCode; //币种
    private String unit; //计量单位
    private Date createTime; //创建时间
    private Date updateTime; //更新时间
    private String ts; //时间戳
    private String deleteFlag; //是否删除 0=否，1=是 参见yesorno
}
