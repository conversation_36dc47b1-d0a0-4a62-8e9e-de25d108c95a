package com.fls.invoice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("t_invoice_elec")
public class InvoiceElec {
    @TableId(value = "id_invoice_elec", type = IdType.ASSIGN_UUID)
    private String idInvoiceElec; //主键
    private String billNo; //单据号
    private String seller; //销售方名称 1-佛朗斯 2-鹏泽 默认 1
    private String buyer; //购买方名称
    private String buyerTaxid; //购买方纳税人识别号
    private String buyerAddress; //购买方地址
    private String buyerTel; //购买方电话
    private String buyerBank; //购买方银行
    private String buyerBankCard; //购买方银行卡号
    private String taxFlag; //是否含税 1-是 0-否
    private String invName; //料品名称
    private Date billDate; //立账日期
    private BigDecimal totalMoney; //合计金额
    private BigDecimal totalTax; //合计税额
    private BigDecimal totalTaxMoney; //价税合计
    private BigDecimal totalNum; //明细总数量
    private String memo; //备注
    private String status; //开票状态 0-未开票 1-开票成功 2-开票失败
    private String invoiceRequestId; //发票请求流水号
    private String invoiceNo; //全电发票号码
    private Date invoiceTime; //开票时间
    private String invoiceDownUrl; //发票下载地址
    private String pdfDownUrl; //发票PDF下载地址
    private String bizNotifyUrl; //业务系统异步通知url
    private Date createTime; //创建时间
    private Date updateTime; //更新时间
    private String ts; //时间戳
    private String deleteFlag; //是否删除 0=否，1=是 参见yesorno
}
