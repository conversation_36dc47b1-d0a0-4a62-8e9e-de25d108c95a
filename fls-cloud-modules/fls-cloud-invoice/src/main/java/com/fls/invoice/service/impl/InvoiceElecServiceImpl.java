package com.fls.invoice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.invoice.constant.SysDictionary;
import com.fls.invoice.entity.InvoiceElec;
import com.fls.invoice.mapper.InvoiceElecMapper;
import com.fls.invoice.service.InvoiceElecService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class InvoiceElecServiceImpl extends ServiceImpl<InvoiceElecMapper, InvoiceElec> implements InvoiceElecService {
    @Override
    public InvoiceElec getInvoiceElec(String billNo) {
        QueryWrapper<InvoiceElec> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_no",billNo);
        queryWrapper.eq("delete_flag", SysDictionary.YESORNO_NO);
        InvoiceElec invoiceElec = baseMapper.selectOne(queryWrapper);
        return invoiceElec;
    }

    @Override
    public int updateBatchParam(Map<String, Object> param) {
        return baseMapper.updateBatchParam(param);
    }

}
