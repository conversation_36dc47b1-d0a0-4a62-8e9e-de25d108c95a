package com.fls.invoice.service;

import com.alibaba.fastjson.JSONObject;
import com.fls.invoice.entity.dto.InvoiceElecDto;

import java.util.List;
import java.util.Map;

public interface InvoiceService {
    /**
     * 推送开票信息
     * @param invoiceList
     * @return
     * @throws Exception
     */
    Map<String, Object> push(List<InvoiceElecDto> invoiceList) throws Exception;

    /**
     * 回调通知
     * @param uri 请求uri
     * @param method 请求方式 POST/GET
     * @param paramMap url参数键值对
     * @param bodyMap 请求体
     * @return
     * @throws Exception
     */
    Map<String, Object> notify(String uri, String method, Map<String,String> paramMap, JSONObject bodyMap) throws Exception;
}
