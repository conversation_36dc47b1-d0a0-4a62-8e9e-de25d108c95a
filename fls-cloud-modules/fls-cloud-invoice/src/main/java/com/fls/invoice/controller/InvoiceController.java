package com.fls.invoice.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fls.invoice.constant.SysDictionary;
import com.fls.invoice.entity.InvoiceElec;
import com.fls.invoice.entity.InvoiceElecB;
import com.fls.invoice.entity.dto.InvoiceElecDto;
import com.fls.invoice.service.InvoiceService;
import com.fls.invoice.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/api/invoice")
@Slf4j
public class InvoiceController {

    @Resource
    private InvoiceService invoiceService;

    @PostMapping("push")
    public R push(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        log.info("接收开全电发票的请求===>{}",jsonString);
        JSONArray jsonArray = JSONArray.parseArray(jsonString);
        //校验必传参数
        String[] notNullParams={"billNo","seller","buyer","taxFlag","totalNum","invName","bizNotifyUrl"};
        Map<String, Object> checkResult = checkRequired(jsonArray, notNullParams);
        if(!checkResult.isEmpty()){
            return R.fail((String)checkResult.get("msg"));
        }
        //校验销售方是否同一个销售方
        Map<String, Object> checkResult2 = checkUnique(jsonArray, "seller");
        if(!checkResult2.isEmpty()){
            return R.fail((String)checkResult2.get("msg"));
        }

        //返回受理结果
        Map<String,Object> map = null;
        try {
            map = invoiceService.push(getInvoiceList(jsonArray));
            //code值超过127
            if(SysDictionary.RESULT_SUCCESS.equals(map.get("code"))){
                return R.ok().setData((Map)map.get("data"));
            }else{
                return R.fail((String)map.get("msg"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }

    }

    @PostMapping("notify")
    public Map<String,Object> notify(HttpServletRequest request, HttpServletResponse response, @RequestBody String jsonString){
        log.info("慧电票的回调通知===>{}",jsonString);
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        String uri = request.getRequestURI();
        String method = request.getMethod();
        Map<String,String> paramMap = getParameterMap(request);

        //返回受理结果
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Map<String, Object> r = new HashMap<String, Object>();
        try {
            r = invoiceService.notify(uri, method, paramMap, jsonObject);
            resultMap.put("head", r);
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            r.put("errorCode", "500");
            r.put("errorMsg", "签收失败");
            r.put("timestamp", System.currentTimeMillis());
            resultMap.put("head", r);
            return resultMap;
        }
    }

    private List<InvoiceElecDto> getInvoiceList(JSONArray jsonArray) throws ParseException {
        List<InvoiceElecDto> invoiceList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            InvoiceElecDto invoiceElecDto = new InvoiceElecDto();
            BeanUtils.copyProperties(getInvoiceElec(jsonArray.getJSONObject(i)),invoiceElecDto);
            invoiceElecDto.setbList(getBList(jsonArray.getJSONObject(i)));
            invoiceList.add(invoiceElecDto);
        }
        return invoiceList;
    }

    private InvoiceElec getInvoiceElec(JSONObject jsonObject) throws ParseException {
        InvoiceElec invoiceElec = new InvoiceElec();
        invoiceElec.setBillNo((String)jsonObject.get("billNo"));
        invoiceElec.setSeller((String)jsonObject.get("seller"));
        invoiceElec.setBuyer((String)jsonObject.get("buyer"));
        invoiceElec.setBuyerTaxid((String)jsonObject.get("buyerTaxid"));
        invoiceElec.setBuyerAddress((String)jsonObject.get("buyerAddress"));
        invoiceElec.setBuyerTel((String)jsonObject.get("buyerTel"));
        invoiceElec.setBuyerBank((String)jsonObject.get("buyerBank"));
        invoiceElec.setBuyerBankCard((String)jsonObject.get("buyerBankCard"));
        invoiceElec.setTaxFlag((String)jsonObject.get("taxFlag"));
        invoiceElec.setInvName((String)jsonObject.get("invName"));
        invoiceElec.setBillDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse((String)jsonObject.get("billDate")));
        invoiceElec.setTotalMoney(jsonObject.get("totalMoney")==null?null:new BigDecimal(jsonObject.get("totalMoney").toString()));
        invoiceElec.setTotalTax(jsonObject.get("totalTax")==null?null:new BigDecimal(jsonObject.get("totalTax").toString()));
        invoiceElec.setTotalTaxMoney(jsonObject.get("totalTaxMoney")==null?null:new BigDecimal(jsonObject.get("totalTaxMoney").toString()));
        invoiceElec.setTotalNum(new BigDecimal(jsonObject.get("totalNum").toString()));
        invoiceElec.setMemo((String)jsonObject.get("memo"));
        invoiceElec.setBizNotifyUrl((String)jsonObject.get("bizNotifyUrl"));

        return invoiceElec;
    }

    private List<InvoiceElecB> getBList(JSONObject jsonObject){
        JSONArray jsonArray = jsonObject.getJSONArray("bList");
        List<InvoiceElecB> bList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            InvoiceElecB invoiceElecB = new InvoiceElecB();
            invoiceElecB.setInvName((String)jsonArray.getJSONObject(i).get("invName"));
            invoiceElecB.setInvCode((String)jsonArray.getJSONObject(i).get("invCode"));
            invoiceElecB.setInvSpec((String)jsonArray.getJSONObject(i).get("invSpec"));
            invoiceElecB.setInvMeasdoc((String)jsonArray.getJSONObject(i).get("invMeasdoc"));
            invoiceElecB.setNum(new BigDecimal(jsonArray.getJSONObject(i).get("num").toString()));
            invoiceElecB.setPrice(new BigDecimal(jsonArray.getJSONObject(i).get("price").toString()));
            invoiceElecB.setMoney(new BigDecimal(jsonArray.getJSONObject(i).get("money").toString()));
            invoiceElecB.setTaxPrice(new BigDecimal(jsonArray.getJSONObject(i).get("taxPrice").toString()));
            invoiceElecB.setTaxMoney(new BigDecimal(jsonArray.getJSONObject(i).get("taxMoney").toString()));
            invoiceElecB.setTaxRate(new BigDecimal(jsonArray.getJSONObject(i).get("taxRate").toString()));
            invoiceElecB.setTax(jsonArray.getJSONObject(i).get("tax")==null?null:new BigDecimal(jsonArray.getJSONObject(i).get("tax").toString()));
            invoiceElecB.setCurrCode((String)jsonArray.getJSONObject(i).get("currCode"));
            invoiceElecB.setUnit((String)jsonArray.getJSONObject(i).get("unit"));

            bList.add(invoiceElecB);
        }
        return bList;
    }

    private Map<String,Object> checkRequired(JSONArray jsonArray, String[] params){
        Map<String,Object> rtn = new HashMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            for(String param:params){
                if(jsonArray.getJSONObject(i).get(param)==null){
                    rtn.put("code", "500");
                    rtn.put("msg", "缺少参数"+"[" + param + "]");
                    break;
                }
            }
        }

        return rtn;
    }

    private Map<String,Object> checkUnique(JSONArray jsonArray, String param){
        Map<String,Object> rtn = new HashMap<>();
        Set<Object> vSet = new HashSet<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            vSet.add(jsonArray.getJSONObject(i).get(param));
        }
        if(vSet.size()>1){
            rtn.put("code", "500");
            rtn.put("msg", "必须同一个值"+"[" + param + "]");
        }
        return rtn;
    }

    private Map<String,String> getParameterMap(HttpServletRequest request) {
        Map properties = request.getParameterMap();
        //返回值Map
        Map<String,String> returnMap = new HashMap();
        Iterator entries = properties.entrySet().iterator();
        Map.Entry entry;
        String name= "";
        String value= "";
        while(entries.hasNext()) {
            entry = (Map.Entry) entries.next();
            name = (String) entry.getKey();
            Object valueObj = entry.getValue();
            if(null == valueObj){
                value = "";
            }else if(valueObj instanceof String[]){
                String[] values = (String[])valueObj;
                for(int i=0;i<values.length;i++){
                    value = values[i] + ",";
                }
                value = value.substring(0, value.length()-1);
            }else{
                value = valueObj.toString();
            }
            returnMap.put(name, value);
        }
        return returnMap;
    }
}
