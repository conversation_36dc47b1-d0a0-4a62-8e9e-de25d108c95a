package com.fls.invoice.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.TreeMap;

public class SignUtil {

	private static final Charset UTF8 = StandardCharsets.UTF_8;
	
    /**
     * 签名
     *
     * @param secretKey 秘钥
     * @param sigStr 签名串
     * @param sigMethod 签名算法
     * @return java.lang.String
     */
    public static String sign(String secretKey, String sigStr, String sigMethod) {
        String sig;
        try {
            Mac mac = Mac.getInstance(sigMethod);
            byte[] hash;
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(UTF8),
                    mac.getAlgorithm());
            mac.init(secretKeySpec);
            hash = mac.doFinal(sigStr.getBytes(UTF8));
            sig = DatatypeConverter.printBase64Binary(hash);
        } catch (Exception e) {
            throw new RuntimeException(e.getClass().getName() + "-" + e.getMessage());
        }
        return sig;
    }
    
    /**
     * 拼接签名源文字符串
     *
     * @param requestParams 请求参数
     * @param reqMethod 请求方法
     * @param path 资源路径
     * @return
     * POSThttps://ioatest.fls123.com/notify?Nonce=883650&SecretId=01000109&Timestamp=1672633154340
     */
    public static String makeSignPlainText(TreeMap<String, String> requestParams, String
            reqMethod, String path) {
        String retStr = "";
        retStr += reqMethod;
        retStr += path;
        retStr += buildParamStr(requestParams);
        return retStr;
    }
    
    public static String buildParamStr(TreeMap<String, String> requestParams) {
        String retStr = "";
        for (String key : requestParams.keySet()) {
            String value = requestParams.get(key);
            if (retStr.length() == 0) {
                retStr += '?';
            } else {
                retStr += '&';
            }
            retStr += key.replace("_", ".") + '=' + value;
        }
        return retStr;
    }

}
