<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fls.invoice.mapper.InvoiceElecMapper">
    <update id="updateBatchParam" parameterType="string">
        update t_invoice_elec
        <set>
            <if test="status != null">status=#{status},</if>
            <if test="invoiceRequestId != null">invoice_request_id=#{invoiceRequestId},</if>
            <if test="invoiceNo != null">invoice_no=#{invoiceNo},</if>
            <if test="invoiceTime != null">invoice_time=#{invoiceTime},</if>
            <if test="invoiceDownUrl != null">invoice_down_url=#{invoiceDownUrl},</if>
            <if test="pdfDownUrl != null">pdf_down_url=#{pdfDownUrl},</if>
            <if test="ts != null">ts=#{ts}</if>
        </set>
        where bill_no in
            <foreach collection="billnos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </update>
</mapper>