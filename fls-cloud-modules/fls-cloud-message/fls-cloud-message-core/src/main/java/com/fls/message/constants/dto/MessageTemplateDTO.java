package com.fls.message.constants.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/29 9:53
 */
@Data
public class MessageTemplateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String title;

    /**
     * 备用发送人群
     */
    private String crowds;

    /**
     * 发送的Id类型
     */
    @NotNull(message = "接收者id类型不能为空")
    private Integer idType;

    /**
     * 发送渠道
     */
    @NotNull(message = "发送渠道不能为空")
    private Integer sendChannel;

    /**
     * 消息类型
     */
    @NotNull(message = "消息类型不能为空")
    private Integer msgType;

    /**
     * 渠道模板信息
     */
    @NotBlank(message = "渠道信息不能为空")
    private String msgContent;

    /**
     * 发送账号（邮件下可有多个发送账号、短信可有多个发送账号..）
     */
    @NotBlank(message = "发送账号不能为空")
    private String sendAccount;
}
