package com.fls.message.constants.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/28 8:55
 */
@Data
public class ChannelAccountDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;

    /**
     * 关联渠道
     */
    @NotNull(message = "关联渠道不能为空")
    private Integer sendChannel;

    /**
     * 账号配置
     */
    @NotBlank(message = "账号配置信息不能为空")
    private String accountConfig;
}
