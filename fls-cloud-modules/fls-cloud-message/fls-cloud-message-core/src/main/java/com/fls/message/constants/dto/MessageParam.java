package com.fls.message.constants.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: jiepeng.cen
 * @Description: 消息参数
 * @Date: create in 2022/12/29 14:12
 */
@Data
public class MessageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否使用模板人群
     */
    private Boolean templateCrowd;

    /**
     * 接收者
     */
    private String receiver;

    /**
     * 消息内容中可变部分
     */
    private Map<String, String> variables;
}
