package com.fls.message.constants.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: jiepeng.cen
 * @Description: 消息渠道配置传输类
 * @Date: create in 2022/12/24 15:06
 */
@Data
public class ChannelConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 渠道标题
     */
    @NotBlank(message = "渠道标题不能为空")
    private String title;

    /**
     * 渠道类型
     */
    @NotNull(message = "渠道类型不能为空")
    private Integer channelType;

    /**
     * 配置详情
     */
    @NotBlank(message = "渠道配置不能为空")
    private String configDetail;
}
