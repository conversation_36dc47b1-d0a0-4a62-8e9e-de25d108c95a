package com.fls.message.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/29 17:01
 */
@Getter
@AllArgsConstructor
public enum IdType {
    USER_ID(1, "userId"),
    DID(2, "did"),
    PHONE(3, "phone"),
    OPEN_ID(4, "openId"),
    EMAIL(5, "email"),
    ENTERPRISE_USER_ID(6, "enterprise_user_id"),
    DING_DING_USER_ID(7, "ding_ding_user_id"),
    CID(8, "cid"),
    FEI_SHU_USER_ID(9, "fei_shu_user_id"),
    ;

    private final Integer code;
    private final String description;
}
