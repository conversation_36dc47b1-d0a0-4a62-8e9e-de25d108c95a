package com.fls.message.constants.enums;

import com.fls.message.constants.bo.EmailContentModel;
import com.fls.message.constants.bo.SmsContentModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: jiepeng.cen
 * @Description:
 * @Date: create in 2022/12/30 14:06
 */
@Getter
@AllArgsConstructor
public enum ChannelType {

    ALI_SMS(1, "阿里云短信", SmsContentModel.class),

    EMAIL(2, "邮件", EmailContentModel.class),
    ;

    /**
     * 编码值
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 内容模型Class
     */
    private final Class contentModelClass;

    /**
     * 通过code获取class
     * @param code 编码
     * @return 类对象
     */
    public static Class getChannelModelClassByCode(Integer code){
        ChannelType[] values = values();
        for (ChannelType value : values) {
            if (value.getCode().equals(code)){
                return value.getContentModelClass();
            }
        }
        return null;
    }
}
