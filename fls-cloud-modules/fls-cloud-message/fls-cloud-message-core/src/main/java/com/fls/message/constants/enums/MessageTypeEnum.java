package com.fls.message.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: jiepeng.cen
 * @Description: 消息类型
 * @Date: create in 2023/1/6 9:51
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {

    NOTICE(1,"通知类消息"),
    MARKETING(2,"营销类消息"),
    AUTH_CODE(3,"验证码消息");

    /**
     * 编码值
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    /**
     * 通过code获取enum
     * @param code 编码
     * @return MessageTypeEnum
     */
    public static MessageTypeEnum getEnumByCode(Integer code) {
        MessageTypeEnum[] values = values();
        for (MessageTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
