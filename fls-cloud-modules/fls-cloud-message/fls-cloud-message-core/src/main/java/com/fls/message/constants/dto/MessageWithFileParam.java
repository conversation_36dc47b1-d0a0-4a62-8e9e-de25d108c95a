package com.fls.message.constants.dto;

import lombok.Data;

import java.io.File;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: jiepeng.cen
 * @Description: 带文件消息参数
 * @Date: create in 2023/2/8 10:28
 */
@Data
public class MessageWithFileParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否使用模板人群
     */
    private Boolean templateCrowd;

    /**
     * 接收者
     */
    private String receiver;

    /**
     * 消息内容中可变部分
     */
    private Map<String, String> variables;

    /**
     * 主题
     */
    private String title;

    /**
     * 用于接口上用户传入的自定义内容
     */
    private String content;

    /**
     * 文件列表
     */
    private List<File> files;
}
